// MongoDB Playground
// Use Ctrl+Space inside a snippet or a string literal to trigger completions.

// The current database to use.
use('elephantisland');

// Create a new document in the collection.
db.getCollection('blogs').insertOne({
    "image": "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/elephantisland%2Finfo-markers%2F1752344002830.jpg?alt=media&token=ab99b2fa-1849-458a-905b-85ae768d175f",
  "title": "<p>Help</p>",
  "body1": "<p><strong><em>\" WELCOME TO OUR LOUNGE AREA</em></strong></p><p><em>An open plan space in a home dedicated to </em><strong><em>relaxation</em></strong><em>, </em><strong><em>socializing</em></strong><em> and </em><strong><em>entertainment</em></strong><em>.</em><strong><em>'' </em></strong></p><p><br></p><p><em>﻿This gorgeous homely space is versatile, where guests can unwind, engage in conversations, watch TV, magazines and novels readily available, or spend quality time with family and friends.</em></p>",
  "body2": "<p><br></p><p>It is fully furnished with comfortable seating including an L shaped detachable sofa, a vintage Red couch, coffee table and an entertainment system with a TV setup.</p>",
  "secondaryEntries": [
    {
      "image": "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/elephantisland%2Finfo-markers%2F1752344067884.jpg?alt=media&token=9ef23660-5e48-4d59-a681-22b29866fcc0",
      "title": "<p>What To Do</p>",
      "body": "<p><strong>Reading and Meditation</strong>:</p><p>Use a comfortable corner with a soft chair or beanbag, along with plants and candles, to create a peaceful reading or meditation spot.&nbsp;</p><p><br></p><p>Social Activities</p><p><strong>Socializing</strong>:</p><p>The living room is a central space for conversations and connecting with friends and family.&nbsp;</p><p><br></p><p><strong>Movie and TV Watching</strong>:</p><p>Arrange your seating to face the television for cozy movie nights with family and friends.&nbsp;</p><p><br></p><p><strong>Listening to Music</strong>:</p><p>Use a calm, ambient soundtrack to create a relaxing atmosphere, or set up a dedicated music corner away from the TV.</p><p><br></p><p><strong>Hosting Gatherings</strong>:</p><p>From casual get-togethers to special occasion dinners, the living room can serve as a venue for various celebrations.&nbsp;</p><p><br></p><p><strong>Board Games and Other Fun Activities</strong>:</p><p>Engage in light-hearted fun by playing board games, cards, or other activities that don't require a lot of space.&nbsp;</p>",
      "body2": "<p><strong>READING AND MEDITATION</strong></p><p>Use a comfortable corner with a soft chair or beanbag, along with plants and candles, to create a peaceful reading or meditation spot.&nbsp;</p><p><br></p><p><strong>SOCIALIZING</strong></p><p>The living room is a central space for conversations and connecting with friends and family.&nbsp;</p><p><br></p><p><strong>MOVIE AND TV WATCHING</strong></p><p>Arrange your seating to face the television for cozy movie nights with family and friends.&nbsp;</p><p><br></p><p><strong>LISTENING TO MUSIC</strong></p><p>Use a calm, ambient soundtrack to create a relaxing atmosphere, or set up a dedicated music corner away from the TV.</p><p><br></p><p><strong>HOSTING GATHERINGS</strong></p><p>From casual get-togethers to special occasion dinners, the living room can serve as a venue for various celebrations.&nbsp;</p><p><br></p><p><strong>BOARD GAMES AND OTHER FUN ACTIVITIES</strong></p><p>Engage in light-hearted fun by playing board games, cards, or other activities that don't require a lot of space.&nbsp;</p>"
    },
    {
      "image": "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/elephantisland%2Finfo-markers%2F1752344142218.jpg?alt=media&token=4fb2baa4-2783-4f34-abaa-cc87ac4d5e15",
      "title": "Lorem ipsum   dolor  ",
      "body": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum quis nunc arcu. Mauris tincidunt \nlaoreet arcu. Nullam lobortis commodo massa, id euismod diam viverra et. ",
      "body2": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum quis nunc arcu. Mauris tincidunt \nlaoreet arcu. Nullam lobortis commodo massa, id euismod diam viverra et. "
    }
  ],
});
