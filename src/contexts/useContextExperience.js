'use client'

import { createContext, useContext, useReducer, useState, useRef, useEffect } from "react"
import { experienceReducer,INITIAL_EXPERIENCE_STATE, ACTIONS_EXPERIENCE_STATE } from "./reducerExperience"
import { usePathname } from 'next/navigation'

const ExperienceContext=createContext()

export function ExperienceContextProvider({children}) {
    const [menuToggle, setMenuToggle] = useState(false);
    const [experienceState,originalDispatch]=useReducer(experienceReducer,INITIAL_EXPERIENCE_STATE)

    // Add debouncing to prevent rapid successive dispatches
    const lastDispatchRef = useRef(null);
    const dispatchTimeoutRef = useRef(null);

    // Wrap dispatch with logging and debouncing to prevent race conditions
    const disptachExperience = (action) => {
        const now = Date.now();
        const timeSinceLastDispatch = lastDispatchRef.current ? now - lastDispatchRef.current : Infinity;

        // console.log('🚀 DISPATCH CALLED:', {
        //     action,
        //     timestamp: new Date().toISOString(),
        //     timeSinceLastDispatch,
        //     stackTrace: new Error().stack?.split('\n').slice(1, 4).join('\n')
        // });

        // Clear any pending dispatch
        if (dispatchTimeoutRef.current) {
            clearTimeout(dispatchTimeoutRef.current);
        }

        // For popup actions, add a small delay to prevent race conditions
        if (action.type.includes('POPUP')) {
            dispatchTimeoutRef.current = setTimeout(() => {
                lastDispatchRef.current = Date.now();
                originalDispatch(action);
            }, 10); // 10ms delay to prevent race conditions
        } else {
            // Non-popup actions dispatch immediately
            lastDispatchRef.current = now;
            originalDispatch(action);
        }
        };
    // Track navigation source to manage landingPageVideoPlayback
    const pathname = usePathname();
    const prevPathRef = useRef(null);

    useEffect(() => {
        const prev = prevPathRef.current;
        if (prev !== pathname) {
            const goingTo360 = pathname?.startsWith('/360s');
            const fromHeroVideo = prev?.startsWith('/hero-video');
            if (goingTo360) {
                // If arriving at 360s from hero-video, enable playback; otherwise disable
                disptachExperience({
                    type: ACTIONS_EXPERIENCE_STATE.SET_LANDING_PAGE_VIDEO_PLAYBACK,
                    payload: !!fromHeroVideo
                });
            }
            prevPathRef.current = pathname;
        }
    }, [pathname]);


  return <ExperienceContext.Provider value={{
        experienceState,disptachExperience,
        menuToggle, setMenuToggle
    }}>
        {children}
    </ExperienceContext.Provider>
}

export function useContextExperience(){
    const context=useContext(ExperienceContext)
    if(!context){
        throw new Error('useExperienceContext must be used within a ExperienceContextProvider');
    }
    return context
}
