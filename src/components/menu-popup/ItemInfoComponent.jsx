'use client'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import { useEffect, useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import SpinerComponent from '../SpinerComponent'
import HtmlContentDisplay from '@/components/HtmlContentDisplay'
import { cleanTextFields } from '@/utils/textUtils'


export default function ItemInfoComponent() {
  const [error,setError]=useState('')
  const [showError,setShowError]=useState(false)
  const [loading,setLoading]=useState(false)
  const [data,setData]=useState(null)
  const {experienceState,disptachExperience}=useContextExperience()

  const fetchData = async (id) => {
    console.log('fetchData:',id)
    try {
      setLoading(true)
      setError('')
      setShowError(false)
      
      const serverResponse = await fetch(`/api/info-markers/${id}`)

      // Check if response is ok
      if (!serverResponse.ok) {
        throw new Error(`HTTP ${serverResponse.status}: ${serverResponse.statusText}`)
      }

      // Check if response is JSON
      const contentType = serverResponse.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await serverResponse.text()
        console.error('Non-JSON response received:', responseText)
        throw new Error('Server returned non-JSON response. This might be a server error.')
      }

      const responseData = await serverResponse.json()
      // console.log('API Response:', responseData)

      // Check if the API response indicates success
      if (!responseData.success) {
        throw new Error(responseData.message || 'Failed to load data')
      }

      // Check if data exists in response
      if (!responseData.data) {
        throw new Error('No data found for this info marker')
      }

      // Clean HTML tags and markup from text fields before setting state
      // const cleanedData = cleanTextFields(responseData.data);
      setData(responseData.data)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching info marker:', error)
      setError(error.message || 'Failed to load data')
      setShowError(true)
      setLoading(false)
    }
  }

  useEffect(() => {
    console.log('ItemInfoComponent mounted/updated:', {
      showItemInfo: experienceState?.showItemInfo,
      showPopupGeneral: experienceState?.showPopupGeneral,
      id: experienceState?.showItemInfo?.id
    });

    if (experienceState?.showItemInfo?.id) {
      fetchData(experienceState?.showItemInfo?.id);
    } else {
      console.warn('ItemInfoComponent: No ID provided in showItemInfo');
    }
  }, [experienceState?.showItemInfo?.id])

  console.log('ItemInfoComponent render:', {
    data,
      // showItemInfo: experienceState?.showItemInfo,
      // showPopupGeneral: experienceState?.showPopupGeneral,
      // loading,
      // hasData: !!data,
      // error
  });
  
  return (
    <div className='flex w-full h-fit text-white'>
      {loading
        ? <div className='flex w-full h-full items-center justify-center'><SpinerComponent/></div>
        : showError
          ? <div className='flex w-full h-full items-center justify-center mt-16'>
              <div className='text-center text-red-400'>
                <h3 className='text-2xl mb-4'>Error Loading Article</h3>
                <p className='text-lg'>{error}</p>
              </div>
            </div>
          : <div className='flex w-full h-full flex-col items-start justify-start mt-16'>
            {/* <ImageWrapperResponsive src={data?.image} className='w-full h-full'/> */}
            <div className='flex flex-col mb-20 w-full h-fit items-center justify-start gap-5 overflow-y-auto'>
              <div className='flex flex-col w-full h-fit items-center justify-start gap-10'>
                <div className='flex font-thin w-full h-auto items-center justify-center relative'>
                  <img src={data?.image} alt='page image' className='object-cover h-auto w-full'/>
                </div>
                <div className='flex w-full h-fit gap-10 flex-col lg:flex-row'>
                  <div className='flex flex-col lg:flex-row flew-row w-full h-fit gap-4'>
                    <div className='max-w-60 text-6xl text-left text-wrap leading-12 uppercase'>
                      <HtmlContentDisplay htmlString={data?.title}/>
                    </div>
                    <div className='flex flex-col max-w-full md:max-w-[676px] gap-4'>
                      <div className='text-left text-[28px] leading-8'>
                        <HtmlContentDisplay htmlString={data?.body1}/>
                      </div>
                      <div className='text-left -text-[28px] leading-[22px]'>
                        <HtmlContentDisplay htmlString={data?.body2}/>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='flex flex-col w-full h-fit mt-10 gap-20'>
                  {data?.secondaryEntries?.map((i,index)=>(
                    <div key={index} className='flex flex-col md:flex-row md:even:flex-row-reverse w-full h-fit items-start justify-start gap-10'>

                      <div className='flex h-fit w-fit relative items-center justify-center'>
                        <img src={i?.image} alt='page image' className='object-cover w-auto h-full'/>
                      </div>
                      <div className='flex max-w-full lg:w-[calc(100%-474px)] flex-col h-fit gap-4'>
                        <div className='w-full uppercase text-[40px] text-left leading-8'>
                          <HtmlContentDisplay htmlString={i?.title}/>
                        </div>
                        <div>
                        <div className='w-full text-left leading-6 text-[20px]'>
                          <HtmlContentDisplay htmlString={i?.body1}/>
                        </div>
                        <div className='w-full text-left leading-[22px] -text-[20px]'>
                          <HtmlContentDisplay htmlString={i?.body2}/>
                        </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
      }
    </div>
  )
}
