'use client'
import React from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useEffect, useState, useCallback } from 'react'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import SpinerComponent from '../SpinerComponent'
import VideoPlayer from './VideoPlayer'
import Image from 'next/image'

function RoverOverButton({data,handleVideoClick}) {
  const [onhover,setOnHover]=useState(false)

  const handleClick = useCallback(() => {
    console.log('🎯 RoverOverButton - Click detected:', {
      data: data ? {
        title: data.title,
        url: data.url,
        _id: data._id
      } : null,
      timestamp: new Date().toISOString()
    })

    if (handleVideoClick && data) {
      handleVideoClick(data)
    } else {
      console.error('❌ RoverOverButton - Missing handler or data:', { handleVideoClick: !!handleVideoClick, data: !!data })
    }
  }, [data, handleVideoClick])

  return(
    <div
      onClick={handleClick}
      onMouseEnter={()=>setOnHover(true)}
      onMouseLeave={()=>setOnHover(false)}
      onTouchStart={()=>setOnHover(true)} // Safari mobile support
      onTouchEnd={()=>setOnHover(false)}  // Safari mobile support
      className='z-10 absolute w-fit h-fit m-auto cursor-pointer'
      role="button"
      tabIndex={0}
      aria-label={`Play video: ${data?.title || 'Untitled'}`}
    >
        {onhover ? <ImageWrapperResponsive src={'assets/video_btn_ov.png'}/> :
        <ImageWrapperResponsive src={'assets/video_btn_off.png'}/>}
    </div>
  )
}

export default function VideoGalleryComponent() {
  const [error,setError]=useState('')
  const [showError,setShowError]=useState(false)
  const [loading,setLoading]=useState(false)
  const [data,setData]=useState(null)
  const [playSingleVideoInGallery,setPlaySingleVideoInGallery]=useState(false)
  const [videoData,setVideoData]=useState({}) // show video player state
  const {experienceState, disptachExperience}=useContextExperience()

  // Handle video player closing
  const handleCloseVideoPlayer = useCallback(() => {
    console.log('🔄 VideoGalleryComponent - Closing video player')
    setPlaySingleVideoInGallery(false)
    setVideoData({})

    // Close popup if needed
    if (experienceState?.showSingleVideoGallery) {
      disptachExperience({
        type: 'POPUP_SINGLE_VIDOE_GALLERY_TOGGLE',
        payload: null
      })
    }
  }, [experienceState?.showSingleVideoGallery, disptachExperience])

  // Safari debugging: Log browser info
  useEffect(() => {
    const userAgent = navigator.userAgent
    const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent)
    const isIOS = /iPad|iPhone|iPod/.test(userAgent)
    console.log('🔍 VideoGalleryComponent - Browser Detection:', {
      userAgent,
      isSafari,
      isIOS,
      timestamp: new Date().toISOString()
    })
  }, [])

  const fetchData = useCallback(async () => {
    try {
      console.log('🔄 VideoGalleryComponent - Starting fetchData')
      setLoading(true)
      setError('')
      setShowError(false)

      const serverResponse = await fetch(`/api/video-gallery`)
      console.log('📡 VideoGalleryComponent - API Response Status:', serverResponse.status)

      if (!serverResponse.ok) {
        throw new Error(`HTTP error! status: ${serverResponse.status}`)
      }

      const responseData = await serverResponse.json()
      console.log('📊 VideoGalleryComponent - API Response Data:', {
        success: responseData?.success,
        dataLength: responseData?.data?.length,
        firstItem: responseData?.data?.[0],
        timestamp: new Date().toISOString()
      })

      if (!responseData?.success || !responseData?.data) {
        throw new Error('Invalid response format or no data received')
      }

      setData(responseData.data)
      setLoading(false)
      console.log('✅ VideoGalleryComponent - Data loaded successfully:', responseData.data.length, 'items')

    } catch (error) {
      console.error('❌ VideoGalleryComponent - fetchData Error:', error)
      setError(error.message)
      setShowError(true)
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  useEffect(() => {
    console.log('🎯 VideoGalleryComponent - Experience State Changed:', {
      showVideoInfo: experienceState?.showVideoInfo,
      dataAvailable: !!data,
      dataLength: data?.length,
      timestamp: new Date().toISOString()
    })
    handleSingleVideoClick()
  }, [data, experienceState?.showVideoInfo])

  const handleSingleVideoClick = useCallback(() => {
    if (!experienceState?.showVideoInfo?.id || !data) {
      console.log('⚠️ VideoGalleryComponent - handleSingleVideoClick: Missing data or video info')
      return
    }

    const matchingVideo = data.find(item => item?.title === experienceState.showVideoInfo.id)
    console.log('🔍 VideoGalleryComponent - Single Video Search:', {
      searchId: experienceState.showVideoInfo.id,
      matchingVideo: matchingVideo ? {
        title: matchingVideo.title,
        url: matchingVideo.url,
        thumbnail: matchingVideo.thumbnail,
        hasDescription: !!matchingVideo.description
      } : null,
      timestamp: new Date().toISOString()
    })

    if (matchingVideo) {
      setVideoData(matchingVideo)
      console.log('✅ VideoGalleryComponent - Single video data set successfully')
    } else {
      console.log('❌ VideoGalleryComponent - No matching video found')
    }
  }, [data, experienceState?.showVideoInfo])

  const handleVideoClick = useCallback((item) => {
    console.log('🎬 VideoGalleryComponent - Video clicked:', {
      item: item ? {
        title: item.title,
        url: item.url,
        thumbnail: item.thumbnail,
        hasDescription: !!item.description,
        _id: item._id
      } : null,
      timestamp: new Date().toISOString()
    })

    if (item && item.url) {
      // Validate the URL before setting
      if (item.url.includes('firebasestorage.googleapis.com')) {
        console.log('✅ VideoGalleryComponent - Firebase Storage URL detected:', item.url)
      } else {
        console.warn('⚠️ VideoGalleryComponent - Non-Firebase URL detected:', item.url)
      }

      setVideoData(item)
      setPlaySingleVideoInGallery(true)
      console.log('✅ VideoGalleryComponent - Gallery video data set successfully:', {
        title: item.title,
        urlLength: item.url.length
      })
    } else {
      console.error('❌ VideoGalleryComponent - Invalid video item clicked:', item)
    }
  }, [])
  
  // Debug experience state changes
  useEffect(() => {
    console.log('🎮 VideoGalleryComponent - Experience State Debug:', {
      showSingleVideoGallery: experienceState?.showSingleVideoGallery,
      showVideoGallery: experienceState?.showVideoGallery,
      playSingleVideoInGallery,
      videoDataExists: !!videoData && Object.keys(videoData).length > 0,
      videoDataUrl: videoData?.url,
      loading,
      error,
      timestamp: new Date().toISOString()
    })
  }, [experienceState?.showSingleVideoGallery, experienceState?.showVideoGallery, playSingleVideoInGallery, videoData, loading, error])

  // Validate video data before rendering VideoPlayer
  const isValidVideoData = useCallback((data) => {
    const isValid = data &&
                   typeof data === 'object' &&
                   data.url &&
                   typeof data.url === 'string' &&
                   data.url.trim() !== ''

    if (!isValid) {
      console.warn('⚠️ VideoGalleryComponent - Invalid video data:', data)
    }

    return isValid
  }, [])

  return (
    <div className='flex mt-16 w-full h-fit text-white'>
      {/* Show error state */}
      {showError && (
        <div className='flex flex-col items-center justify-center w-full h-64 text-red-400'>
          <h2 className='text-xl mb-2'>Error Loading Videos</h2>
          <p className='text-sm text-center'>{error}</p>
          <button
            onClick={() => {
              setShowError(false)
              fetchData()
            }}
            className='mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700'
          >
            Retry
          </button>
        </div>
      )}

      {/* Show loading state */}
      {loading && !showError && <SpinerComponent/>}

      {/* Show content when not loading and no error */}
      {!loading && !showError && (
        <>
          {/* Video Player - Show when we have valid video data and either condition is met */}
          {(() => {
            const shouldShow = isValidVideoData(videoData) && (
              experienceState?.showSingleVideoGallery || playSingleVideoInGallery
            )

            console.log('🎬 VideoPlayer Render Check:', {
              shouldShow,
              isValidVideoData: isValidVideoData(videoData),
              showSingleVideoGallery: experienceState?.showSingleVideoGallery,
              playSingleVideoInGallery,
              videoData: videoData ? { title: videoData.title, hasUrl: !!videoData.url } : null
            })

            return shouldShow ? (
              <div className='w-full'>
                <VideoPlayer data={videoData} setShowVideoPlayer={handleCloseVideoPlayer}/>
              </div>
            ) : null
          })()}

          {/* Regular video gallery view - Show when not playing single video */}
          {experienceState?.showVideoGallery && !playSingleVideoInGallery && (
                <div className='flex flex-col w-full h-full items-start justify-start'>
                  <div className='flex w-full flex-col md:flex-col lg:flex-row items-start'>
                    <h1 className='text-7xl max-w-1/4 leading-14 mt-2'>HOME VIDEOS</h1>
                    <div className='flex flex-col gap-2'>
                      <span className='text-3xl'>Elephant Island offers a collection of home videos showcasing various experiences and moments.</span>
                      <span className='-text-sm'>Below is a series of videos presenting some of the activities and experiences that can be shared with families, Friends and loved ones.</span>
                      <span className='-text-sm'>Email your Home Video at <a className='underline' href="mailto:<EMAIL>"><EMAIL></a> and hopefully your video will feature in the collection.</span>

                      {/* Temporary debug button */}
                      <button
                        onClick={() => {
                          console.log('🧪 Debug - Force trigger video player')
                          if (data && data.length > 0) {
                            const testVideo = data[0]
                            console.log('🧪 Debug - Setting test video:', testVideo)
                            setVideoData(testVideo)
                            setPlaySingleVideoInGallery(true)
                          }
                        }}
                        className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
                      >
                        🧪 Force Video Player
                      </button>
                    </div>
                  </div>

                  {/* Video gallery grid */}
                  {data && data.length > 0 ? (
                    <div className='flex mt-4 flex-wrap w-full'>
                      {data.map((item) => {
                        // Validate each item before rendering
                        if (!item || !item._id || !item.url) {
                          console.warn('⚠️ VideoGalleryComponent - Skipping invalid item:', item)
                          return null
                        }

                        return (
                          <div key={item._id} className='flex min-w-full relative md:w-1/2 md:min-w-1/2 lg:max-w-1/3 lg:min-w-1/3 h-80 flex-col items-center justify-center p-2'>
                            <div className='w-full h-full object-cover rounded-md'>
                              <div className='flex flex-col w-full h-52 items-center justify-center bg-gray-500 relative'>
                                {item?.thumbnail && (
                                  <Image
                                    fill
                                    style={{ objectFit: 'cover' }}
                                    alt={`Video thumbnail for ${item.title}`}
                                    src={item.thumbnail}
                                    onError={(e) => {
                                      console.error('❌ VideoGalleryComponent - Thumbnail load error:', item.thumbnail)
                                      e.target.style.display = 'none'
                                    }}
                                  />
                                )}
                                <RoverOverButton data={item} handleVideoClick={handleVideoClick}/>
                              </div>
                              <div className='flex mt-4 flex-col w-full'>
                                <h1 className='text-xl uppercase font-bold'>
                                  {item?.title || 'Untitled Video'}
                                </h1>
                                <h1 className='text-sm'>
                                  {item?.description || 'Lorem ipsum dolor sit amet consectetur, adipisicing elit. Nisi cupiditate eveniet optio eum iusto fugiat itaque corporis.'}
                                </h1>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  ) : (
                    <div className='flex items-center justify-center w-full h-64 text-gray-400'>
                      <div className='text-center'>
                        <h2 className='text-xl mb-2'>No Videos Available</h2>
                        <p className='text-sm'>Check back later for new content.</p>
                      </div>
                    </div>
                  )}
                </div>
          )}
        </>
      )}
    </div>
  )
}
