import React from 'react'

export default function VideoPlayerTest({ data, setShowVideoPlayer }) {
  console.log('🧪 VideoPlayerTest - Rendering with data:', data,setShowVideoPlayer)
  
  return (
    <div className="w-fit h-fit m-auto fixed inset-0 z-50 flex items-center justify-center">
      <div className=" p-8 rounded-lg max-w-[380px] md:max-w-[800px] mx-4">
        
        {data?.url && (
          <div className="mb-4">
            <video 
              src={data.url}
              controls={false}
              className="rounded-lg bg-black"
              onError={(e) => console.error('🧪 VideoPlayerTest - Video error:', e)}
              onLoadedMetadata={() => console.log('🧪 VideoPlayerTest - Video loaded')}
            />
          </div>
        )}
        
        <div className="flex gap-2">
        </div>
      </div>
    </div>
  )
}
