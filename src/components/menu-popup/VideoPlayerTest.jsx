import React from 'react'

export default function VideoPlayerTest({ data, setShowVideoPlayer }) {
  console.log('🧪 VideoPlayerTest - Rendering with data:', data,setShowVideoPlayer)
  
  return (
    <div className="w-fit h-fit m-auto fixed inset-0 z-50 flex items-center justify-center">
      <div className=" p-8 rounded-lg max-w-[380px] md:max-w-[800px] mx-4">
        {/* <h2 className="text-xl font-bold mb-4 text-black">Video Player Test</h2>
        <div className="mb-4 text-black">
          <p><strong>Title:</strong> {data?.title || 'No title'}</p>
          <p><strong>URL:</strong> {data?.url ? data.url.substring(0, 50) + '...' : 'No URL'}</p>
          <p><strong>Thumbnail:</strong> {data?.thumbnail ? 'Yes' : 'No'}</p>
        </div> */}
        
        {data?.url && (
          <div className="mb-4">
            <video 
              src={data.url}
              controls={false}
              className="rounded-lg bg-black"
              onError={(e) => console.error('🧪 VideoPlayerTest - Video error:', e)}
              onLoadedMetadata={() => console.log('🧪 VideoPlayerTest - Video loaded')}
            />
          </div>
        )}
        
        <div className="flex gap-2">
          {/* <button
            onClick={() => {
              console.log('🧪 VideoPlayerTest - Close clicked')
              if (setShowVideoPlayer) setShowVideoPlayer(false)
            }}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Close
          </button> */}
          {/* <button
            onClick={() => {
              console.log('🧪 VideoPlayerTest - Debug info:', {
                data,
                hasSetShowVideoPlayer: !!setShowVideoPlayer
              })
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Debug
          </button> */}
        </div>
      </div>
    </div>
  )
}
