import React, { useState, useRef, useEffect, useCallback } from 'react'

export default function VideoPlayer({ data, setShowVideoPlayer }) {
  const videoRef = useRef(null)
  const progressRef = useRef(null)
  const volumeRef = useRef(null)
  const controlsTimeoutRef = useRef(null)

  // Video states
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [isBuffering, setIsBuffering] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [retryCount, setRetryCount] = useState(0)
  const [videoUrl, setVideoUrl] = useState('')

  // Safari detection
  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)

  // Process Firebase Storage URL for all browsers
  const processVideoUrl = useCallback((url) => {
    if (!url) return ''

    console.log('🔄 VideoPlayer - Processing video URL:', { url, isSafari, isIOS })

    // Process Firebase Storage URLs for better compatibility
    if (url.includes('firebasestorage.googleapis.com')) {
      try {
        const urlObj = new URL(url)

        // Always add alt=media for direct video access
        urlObj.searchParams.set('alt', 'media')

        // Add cache busting for Safari and other browsers having issues
        if (isSafari || isIOS) {
          urlObj.searchParams.set('cache', 'no-cache')
          urlObj.searchParams.set('t', Date.now().toString())
        }

        // Remove any existing token parameter that might be expired
        if (urlObj.searchParams.has('token')) {
          console.log('🔄 VideoPlayer - Removing existing token parameter')
          urlObj.searchParams.delete('token')
        }

        const processedUrl = urlObj.toString()
        console.log('✅ VideoPlayer - Processed URL:', { original: url, processed: processedUrl })
        return processedUrl
      } catch (error) {
        console.error('❌ VideoPlayer - URL processing failed:', error)
        return url // Return original URL if processing fails
      }
    }

    return url
  }, [isSafari, isIOS])

  // Initialize video URL when data changes
  useEffect(() => {
    if (data?.url) {
      const processed = processVideoUrl(data.url)
      setVideoUrl(processed)
      setHasError(false)
      setErrorMessage('')
      setRetryCount(0)
      setIsLoading(true)
      console.log('🎬 VideoPlayer - Video URL set:', {
        original: data.url,
        processed,
        title: data.title,
        thumbnail: data.thumbnail
      })

      // Reset video element when URL changes
      if (videoRef.current) {
        videoRef.current.currentTime = 0
        setCurrentTime(0)
        setIsPlaying(false)
      }
    }
  }, [data?.url, processVideoUrl])

  // Additional effect to handle video element src changes
  useEffect(() => {
    if (videoRef.current && videoUrl) {
      console.log('🔄 VideoPlayer - Setting video src:', videoUrl)
      videoRef.current.src = videoUrl
      videoRef.current.load()
    }
  }, [videoUrl])

  // Format time helper
  const formatTime = useCallback((time) => {
    if (isNaN(time)) return '0:00'
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }, [])

  // Retry video loading with different strategies
  const retryVideoLoad = useCallback(async () => {
    if (!videoRef.current || !data?.url) return

    const newRetryCount = retryCount + 1
    console.log('🔄 VideoPlayer - Retrying video load, attempt:', newRetryCount)
    setRetryCount(newRetryCount)
    setIsLoading(true)
    setHasError(false)
    setErrorMessage('')

    try {
      let newUrl = ''

      // Strategy 1: Use processed URL with cache busting
      if (newRetryCount === 1) {
        newUrl = processVideoUrl(data.url)
        console.log('🔄 Strategy 1 - Processed URL:', newUrl)
      }
      // Strategy 2: Try direct Firebase URL with alt=media only
      else if (newRetryCount === 2) {
        if (data.url.includes('firebasestorage.googleapis.com')) {
          const baseUrl = data.url.split('?')[0]
          newUrl = `${baseUrl}?alt=media&_t=${Date.now()}`
        } else {
          newUrl = data.url
        }
        console.log('🔄 Strategy 2 - Direct URL:', newUrl)
      }
      // Strategy 3: Try original URL with just cache buster
      else if (newRetryCount === 3) {
        const separator = data.url.includes('?') ? '&' : '?'
        newUrl = `${data.url}${separator}retry=${Date.now()}`
        console.log('🔄 Strategy 3 - Original with cache buster:', newUrl)
      }
      // Strategy 4: Last resort - original URL as-is
      else {
        newUrl = data.url
        console.log('🔄 Strategy 4 - Original URL as-is:', newUrl)
      }

      setVideoUrl(newUrl)

      // Force video element to reload
      if (videoRef.current) {
        videoRef.current.src = newUrl
        videoRef.current.load()

        // Try to play after a short delay
        setTimeout(() => {
          if (videoRef.current && !hasError) {
            videoRef.current.play().catch(err => {
              console.log('🔄 VideoPlayer - Auto-play after retry failed (expected):', err.message)
            })
          }
        }, 500)
      }

    } catch (error) {
      console.error('❌ VideoPlayer - Retry failed:', error)
      setErrorMessage('Failed to load video. Please check your connection.')
      setHasError(true)
      setIsLoading(false)
    }
  }, [data?.url, retryCount, processVideoUrl, hasError])

  // Toggle play/pause with Safari-specific error handling
  const togglePlayPause = useCallback(async () => {
    if (!videoRef.current) return

    if (isPlaying) {
      videoRef.current.pause()
    } else {
      try {
        const playPromise = videoRef.current.play()
        if (playPromise && typeof playPromise.then === 'function') {
          await playPromise
        }
      } catch (err) {
        console.error('❌ VideoPlayer - Play failed:', err)

        // Safari-specific error handling
        if (isSafari) {
          try {
            // Try muted play first (Safari autoplay policy)
            videoRef.current.muted = true
            setIsMuted(true)
            const retryPromise = videoRef.current.play()
            if (retryPromise && typeof retryPromise.then === 'function') {
              await retryPromise
            }
            setErrorMessage('Video started muted due to browser policy. Click to unmute.')
          } catch (err2) {
            console.error('❌ VideoPlayer - Muted play also failed:', err2)
            setErrorMessage('Unable to play video. Try clicking the play button.')
            setShowControls(true)
          }
        } else {
          setErrorMessage('Playback failed. Please try again.')
        }
      }
    }
  }, [isPlaying, isSafari])

  // Handle volume change
  const handleVolumeChange = useCallback((e) => {
    const newVolume = parseFloat(e.target.value)
    setVolume(newVolume)
    if (videoRef.current) {
      videoRef.current.volume = newVolume
      setIsMuted(newVolume === 0)
    }
  }, [])

  // Toggle mute
  const toggleMute = useCallback(() => {
    if (!videoRef.current) return

    if (isMuted) {
      videoRef.current.volume = volume
      setIsMuted(false)
    } else {
      videoRef.current.volume = 0
      setIsMuted(true)
    }
  }, [isMuted, volume])

  // Seek to specific time
  const handleSeek = useCallback((e) => {
    if (!videoRef.current || !progressRef.current) return

    const rect = progressRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const newTime = (clickX / rect.width) * duration

    videoRef.current.currentTime = newTime
    setCurrentTime(newTime)
  }, [duration])

  // Skip forward/backward
  const skipTime = useCallback((seconds) => {
    if (!videoRef.current) return

    const newTime = Math.max(0, Math.min(duration, currentTime + seconds))
    videoRef.current.currentTime = newTime
    setCurrentTime(newTime)
  }, [currentTime, duration])

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      videoRef.current?.requestFullscreen?.()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen?.()
      setIsFullscreen(false)
    }
  }, [])



  // Keyboard controls
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.target.tagName === 'INPUT') return

      switch (e.code) {
        case 'Space':
          e.preventDefault()
          togglePlayPause()
          break
        case 'ArrowLeft':
          e.preventDefault()
          skipTime(-10)
          break
        case 'ArrowRight':
          e.preventDefault()
          skipTime(10)
          break
        case 'ArrowUp':
          e.preventDefault()
          setVolume(prev => Math.min(1, prev + 0.1))
          break
        case 'ArrowDown':
          e.preventDefault()
          setVolume(prev => Math.max(0, prev - 0.1))
          break
        case 'KeyM':
          e.preventDefault()
          toggleMute()
          break
        case 'KeyF':
          e.preventDefault()
          toggleFullscreen()
          break
        default:
          break
      }
    }

    document.addEventListener('keydown', handleKeyPress)
    return () => document.removeEventListener('keydown', handleKeyPress)
  }, [togglePlayPause, skipTime, toggleMute, toggleFullscreen])

  // Auto-hide controls
  useEffect(() => {
    const resetControlsTimeout = () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }

      setShowControls(true)

      if (isPlaying) {
        controlsTimeoutRef.current = setTimeout(() => {
          setShowControls(false)
        }, 3000)
      }
    }

    resetControlsTimeout()

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
    }
  }, [isPlaying])

  return (
    <>
      {/* Custom Styles */}
      <style jsx>{`
        .slider {
          -webkit-appearance: none;
          appearance: none;
          background: rgba(255, 255, 255, 0.3);
          outline: none;
          border-radius: 5px;
        }

        .slider::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 15px;
          height: 15px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid white;
        }

        .slider::-moz-range-thumb {
          width: 15px;
          height: 15px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid white;
        }

        .video-container:fullscreen {
          background: black;
        }

        .video-container:fullscreen video {
          width: 100vw;
          height: 100vh;
          object-fit: contain;
        }
      `}</style>

      <div className='video-player flex z-10 fixed m-auto left-0 right-0 top-0 bottom-0 min-w-[320px] max-w-[380px] md:max-w-[800px] h-fit'>
          {/* <div
            onClick={handleVideoPlayerClose}
            className=" flex z-10 items-center justify-center absolute top-0 right-[104px] h-[75px] w-[96px] cursor-pointer"
          >
            <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
            <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
          </div>  */}
        <div className='flex relative m-auto w-full h-full items-center justify-center overflow-y-auto overflow-x-hidden'>
          <div className="relative w-full max-w-4xl bg-black rounded-xl shadow-2xl overflow-hidden">
              {/* Video Container */}
              <div
                className="md:w-[800px] w-[380px] h-[180px] md:h-[450px] relative aspect-video bg-black group cursor-pointer"
                onClick={togglePlayPause}
                onMouseMove={() => setShowControls(true)}
              >
                {/* Main Video Element */}
                <video
                  ref={videoRef}
                  src={videoUrl || data?.url}
                  poster={data?.thumbnail}
                  className="video-element w-full h-full object-contain"
                  // Safari-specific attributes
                  playsInline
                  webkit-playsinline="true"
                  muted={isMuted}
                  preload="metadata"
                  crossOrigin="anonymous"
                  controls={false}
                  disablePictureInPicture
                  disableRemotePlayback
                  // Event handlers with enhanced debugging
                  onLoadStart={() => {
                    console.log('🔄 VideoPlayer - Load started for:', videoUrl || data?.url)
                    setIsLoading(true)
                    setHasError(false)
                  }}
                  onLoadedMetadata={() => {
                    console.log('✅ VideoPlayer - Metadata loaded:', {
                      duration: videoRef.current?.duration,
                      videoWidth: videoRef.current?.videoWidth,
                      videoHeight: videoRef.current?.videoHeight,
                      readyState: videoRef.current?.readyState
                    })
                    if (videoRef.current) {
                      setDuration(videoRef.current.duration)
                      setIsLoading(false)
                      videoRef.current.volume = volume
                      videoRef.current.muted = isMuted
                    }
                  }}
                  onCanPlay={() => {
                    console.log('✅ VideoPlayer - Can play - ready for playback')
                    setIsBuffering(false)
                    setIsLoading(false)
                  }}
                  onCanPlayThrough={() => {
                    console.log('✅ VideoPlayer - Can play through - fully loaded')
                    setIsBuffering(false)
                    setIsLoading(false)
                  }}
                  onTimeUpdate={() => videoRef.current && setCurrentTime(videoRef.current.currentTime)}
                  onPlay={() => {
                    console.log('▶️ VideoPlayer - Playing started')
                    setIsPlaying(true)
                    setIsBuffering(false)
                    setErrorMessage('')
                  }}
                  onPause={() => {
                    console.log('⏸️ VideoPlayer - Paused')
                    setIsPlaying(false)
                  }}
                  onWaiting={() => {
                    console.log('⏳ VideoPlayer - Waiting/Buffering')
                    setIsBuffering(true)
                  }}
                  onError={(e) => {
                    const error = e.target.error
                    const currentSrc = e.target.currentSrc || e.target.src

                    console.error('❌ VideoPlayer - Video error:', {
                      code: error?.code,
                      message: error?.message,
                      currentSrc,
                      networkState: e.target.networkState,
                      readyState: e.target.readyState,
                      retryCount
                    })

                    setHasError(true)
                    setIsLoading(false)
                    setIsBuffering(false)

                    const errorMessages = {
                      1: 'Video loading was aborted. Please try again.',
                      2: 'Network error occurred. Check your connection and try again.',
                      3: 'Video format not supported or corrupted.',
                      4: 'Video format not supported by your browser.'
                    }

                    let errorMsg = errorMessages[error?.code] || 'Unable to load video. Please try again.'

                    // Add specific Firebase Storage error context
                    if (currentSrc && currentSrc.includes('firebasestorage.googleapis.com')) {
                      errorMsg += ' (Firebase Storage URL issue detected)'
                    }

                    setErrorMessage(errorMsg)
                  }}
                  onEnded={() => setIsPlaying(false)}
                  onStalled={() => setIsBuffering(true)}
                />

                {/* Loading Spinner */}
                {(isLoading || isBuffering) && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                    <div className="animate-spin rounded-full h-16 w-16 border-4 border-white border-t-transparent"></div>
                  </div>
                )}

                {/* Error State with Retry */}
                {hasError && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
                    <div className="text-center text-white max-w-sm mx-4">
                      <svg className="mx-auto h-16 w-16 mb-4 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <h3 className="text-lg font-medium mb-2">Video Loading Failed</h3>
                      <p className="text-sm text-gray-300 mb-4">
                        {errorMessage || 'Unable to load video. Please check your connection and try again.'}
                      </p>
                      {retryCount < 4 && (
                        <button
                          onClick={retryVideoLoad}
                          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors mr-2"
                        >
                          Retry ({retryCount + 1}/4)
                        </button>
                      )}
                      <button
                        onClick={() => {
                          console.log('🔍 VideoPlayer - Debug info:', {
                            originalUrl: data?.url,
                            processedUrl: videoUrl,
                            videoElement: {
                              src: videoRef.current?.src,
                              currentSrc: videoRef.current?.currentSrc,
                              networkState: videoRef.current?.networkState,
                              readyState: videoRef.current?.readyState,
                              error: videoRef.current?.error
                            },
                            retryCount,
                            hasError,
                            errorMessage
                          })
                        }}
                        className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors mr-2"
                      >
                        Debug Info
                      </button>
                      <button
                        onClick={() => {
                          if (setShowVideoPlayer) setShowVideoPlayer(false)
                        }}
                        className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                      >
                        Close
                      </button>
                      <div className="mt-4 text-xs text-gray-300">
                        <p><strong>Debug Info:</strong></p>
                        <p>• Original URL: {data?.url ? data.url.substring(0, 50) + '...' : 'None'}</p>
                        <p>• Processed URL: {videoUrl ? videoUrl.substring(0, 50) + '...' : 'None'}</p>
                        <p>• Retry Count: {retryCount}</p>
                        <p>• Browser: {isSafari ? 'Safari' : 'Other'} {isIOS ? '(iOS)' : ''}</p>
                        {isSafari && (
                          <div className="mt-2 text-yellow-300">
                            <p><strong>Safari Tips:</strong></p>
                            <p>• Check your internet connection</p>
                            <p>• Try refreshing the page</p>
                            <p>• Ensure video format is supported</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Center Play Button (when paused) */}
                {!isPlaying && !isLoading && !hasError && (
                  <div onClick={togglePlayPause} className="absolute inset-0 flex items-center justify-center">
                    <div className='flex justify-center items-center w-24 h-24 border-2 border-white bg-gray-700/50 rounded-full'>
                      {isPlaying ? (
                          <svg className="h-20 w-20" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                          </svg>
                        ) : (
                          <svg className="h-20 w-20" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8 5v14l11-7z"/>
                          </svg>
                        )}
                    </div>
                  </div>
                )}

                {/* Safari Error Message */}
                {errorMessage && !hasError && (
                  <div className="absolute top-4 left-4 right-4 bg-yellow-600 bg-opacity-90 text-white p-3 rounded-lg text-sm">
                    <div className="flex items-center">
                      <svg className="h-5 w-5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <span>{errorMessage}</span>
                      <button
                        onClick={() => setErrorMessage('')}
                        className="ml-auto text-white hover:text-gray-300"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                )}

                {/* Video Controls Overlay */}
                <div className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/50 to-transparent p-4 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}>


                  {/* Control Buttons */}
                  <div className="flex flex-col-reverse md:flex-col items-center justify-between">
                    <div className='flex w-full h-fit justify-between flex-col'>
                      {/* Progress Bar */}
                      <div className="w-full">
                        <div
                          ref={progressRef}
                          className="w-full h-1 bg-white bg-opacity-30 rounded-full cursor-pointer hover:h-3 transition-all duration-200"
                          onClick={handleSeek}
                        >
                          <div
                            className="h-full bg-blue-500 rounded-full transition-all duration-200"
                            style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
                          />
                        </div>
                      </div>
                      <div className='flex w-full '>
                        {/* Left Controls */}
                        <div className='flex max-w-1/3 relative flex-col justify-center'>
                          {/* Video COntrols Bar */}
                          <div className="flex items-center space-x-4">
                            {/* Play/Pause */}
                            <button
                              onClick={togglePlayPause}
                              className="text-white hover:text-blue-400 transition-colors p-2"
                              aria-label={isPlaying ? 'Pause' : 'Play'}
                            >
                              {isPlaying ? (
                                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                                </svg>
                              ) : (
                                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M8 5v14l11-7z"/>
                                </svg>
                              )}
                            </button>

                            {/* Skip Backward */}
                            <button
                              onClick={() => skipTime(-10)}
                              className="text-white hover:text-blue-400 transition-colors p-2"
                              aria-label="Skip backward 10 seconds"
                            >
                              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M11.99 5V1l-5 5 5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6h-2c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"/>
                                <text x="12" y="15" textAnchor="middle" fontSize="8" fill="white">10</text>
                              </svg>
                            </button>

                            {/* Skip Forward */}
                            <button
                              onClick={() => skipTime(10)}
                              className="text-white hover:text--400 transition-colors p-2"
                              aria-label="Skip forward 10 seconds"
                            >
                              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 5V1l5 5-5 5V7c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6h2c0 4.42-3.58 8-8 8s-8-3.58-8-8 3.58-8 8-8z"/>
                                <text x="12" y="15" textAnchor="middle" fontSize="8" fill="white">10</text>
                              </svg>
                            </button>

                            {/* Volume Controls */}
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={toggleMute}
                                className="text-white hover:text-blue-400 transition-colors p-2"
                                aria-label={isMuted ? 'Unmute' : 'Mute'}
                              >
                                {isMuted || volume === 0 ? (
                                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
                                  </svg>
                                ) : (
                                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                                  </svg>
                                )}
                              </button>

                              <input
                                ref={volumeRef}
                                type="range"
                                min="0"
                                max="1"
                                step="0.1"
                                value={isMuted ? 0 : volume}
                                onChange={handleVolumeChange}
                                className="w-20 h-1 bg-white bg-opacity-30 rounded-lg appearance-none cursor-pointer slider"
                                aria-label="Volume"
                              />
                            </div>

                            {/* Time Display */}
                            <div className="text-white text-sm font-mono">
                              {formatTime(currentTime)} / {formatTime(duration)}
                            </div>
                          </div>
                        </div>

                        {/* Right Controls */}
                        {false && <div className="flex items-center space-x-2">
                          {/* Fullscreen Toggle */}
                          <button
                            onClick={toggleFullscreen}
                            className="text-white hover:text-blue-400 transition-colors p-2"
                            aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
                          >
                            {isFullscreen ? (
                              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"/>
                              </svg>
                            ) : (
                              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                              </svg>
                            )}
                          </button>
                        </div>}
                      </div>
                    </div>
                    {
                      <div className='flex w-full flex-col justify-center mt-4'>
                        <h1 className='font-bold uppercase leading-5 text-lg'>{data?.title}</h1>
                        <span className='text-sm'>{data?.description}</span>
                      </div>
                    }
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>
    </>
  )
}
