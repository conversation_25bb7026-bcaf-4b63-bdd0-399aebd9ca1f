'use client'
import React, { useState, useEffect } from 'react';

const CountdownTimer = ({ targetDate = new Date('2025-09-30T00:00:00') }) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    isExpired: false
  });

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const target = new Date(targetDate).getTime();
      const difference = target - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({
          days,
          hours,
          minutes,
          seconds,
          isExpired: false
        });
      } else {
        setTimeLeft({
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          isExpired: true
        });
      }
    };

    // Calculate immediately
    calculateTimeLeft();

    // Set up interval to update every second
    const timer = setInterval(calculateTimeLeft, 1000);

    // Cleanup interval on component unmount
    return () => clearInterval(timer);
  }, [targetDate]);

  const formatNumber = (num) => {
    return num.toString().padStart(2, '0');
  };

  if (timeLeft.isExpired) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white px-4">
        <div className="text-center">
          <div className="mb-8">
            <div className="w-24 h-24 mx-auto mb-4 bg-white rounded-full flex items-center justify-center">
              <svg className="w-16 h-16 text-blue-800" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-2">ELEPHANT</h1>
            <h2 className="text-2xl md:text-3xl font-light tracking-wider">ISLAND</h2>
          </div>
          <div className="text-xl md:text-2xl font-semibold mb-4">
            WEBSITE IS NOW LIVE!
          </div>
          <p className="text-lg opacity-90">
            Welcome to Elephant Island
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white px-4">
      <div className="text-center">
        {/* Logo Section */}
        <div className="mb-8">
          <div className="w-24 h-24 mx-auto mb-4 bg-white rounded-full flex items-center justify-center">
            <svg className="w-16 h-16 text-blue-800" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-2">ELEPHANT</h1>
          <h2 className="text-2xl md:text-3xl font-light tracking-wider">ISLAND</h2>
        </div>

        {/* Coming Soon Text */}
        <div className="mb-12">
          <h3 className="text-xl md:text-2xl font-semibold mb-2">WEBSITE</h3>
          <h3 className="text-xl md:text-2xl font-semibold">COMING SOON</h3>
        </div>

        {/* Countdown Display */}
        <div className="flex flex-wrap justify-center gap-4 md:gap-8">
          {/* Days */}
          <div className="flex flex-col items-center bg-white/10 backdrop-blur-sm rounded-lg p-4 md:p-6 min-w-[80px] md:min-w-[100px]">
            <div className="text-3xl md:text-5xl font-bold mb-2">
              {formatNumber(timeLeft.days)}
            </div>
            <div className="text-sm md:text-base font-medium opacity-80">
              DAYS
            </div>
          </div>

          {/* Hours */}
          <div className="flex flex-col items-center bg-white/10 backdrop-blur-sm rounded-lg p-4 md:p-6 min-w-[80px] md:min-w-[100px]">
            <div className="text-3xl md:text-5xl font-bold mb-2">
              {formatNumber(timeLeft.hours)}
            </div>
            <div className="text-sm md:text-base font-medium opacity-80">
              HOURS
            </div>
          </div>

          {/* Minutes */}
          <div className="flex flex-col items-center bg-white/10 backdrop-blur-sm rounded-lg p-4 md:p-6 min-w-[80px] md:min-w-[100px]">
            <div className="text-3xl md:text-5xl font-bold mb-2">
              {formatNumber(timeLeft.minutes)}
            </div>
            <div className="text-sm md:text-base font-medium opacity-80">
              MINUTES
            </div>
          </div>

          {/* Seconds */}
          <div className="flex flex-col items-center bg-white/10 backdrop-blur-sm rounded-lg p-4 md:p-6 min-w-[80px] md:min-w-[100px]">
            <div className="text-3xl md:text-5xl font-bold mb-2">
              {formatNumber(timeLeft.seconds)}
            </div>
            <div className="text-sm md:text-base font-medium opacity-80">
              SECONDS
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-12 text-center">
          <p className="text-lg opacity-90 mb-2">
            Something amazing is coming...
          </p>
          <p className="text-sm opacity-70">
            September 6th, 2025
          </p>
        </div>
      </div>
    </div>
  );
};

export default CountdownTimer;
