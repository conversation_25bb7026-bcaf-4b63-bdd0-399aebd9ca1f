'use client';

import { useState, useEffect } from 'react';




function PaymentFormContent({
  booking,
  onSuccess,
  onError,
  onProcessing,
  savePaymentMethod = false
}) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // DPO-only: no client-side intent or payment methods to load
  }, [booking]);



  const initiateDpoPayment = async () => {
    try {
      setIsProcessing(true);
      setError(null);
      const res = await fetch('/api/payments/dpo/initiate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ bookingId: booking._id }),
      });
      const data = await res.json();
      if (!res.ok || !data.success) {
        throw new Error(data.message || 'Failed to initialize DPO payment');
      }
      const { redirectUrl } = data.data || {};
      if (!redirectUrl) throw new Error('Missing DPO redirect URL');
      // Redirect to hosted payment page
      window.location.href = redirectUrl;
    } catch (err) {
      setError(err.message || 'Failed to start DPO payment');
      onError?.(err);
    } finally {
      setIsProcessing(false);
    }
  };





  const formatAmount = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  return (
    <form className="space-y-6">
      {/* Payment Summary */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Payment Summary</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Package:</span>
            <span>{booking.package?.name}</span>
          </div>

          <div className="flex justify-between">
            <span>Guests:</span>
            <span>{booking.guests.total} ({booking.guests.guestType})</span>
          </div>
          <div className="flex justify-between">
            <span>Subtotal:</span>
            <span>{formatAmount(booking.pricing.basePrice)}</span>
          </div>
          {booking.pricing.taxes > 0 && (
            <div className="flex justify-between">
              <span>Taxes:</span>
              <span>{formatAmount(booking.pricing.taxes)}</span>
            </div>
          )}
          {booking.pricing.fees > 0 && (
            <div className="flex justify-between">
              <span>Fees:</span>
              <span>{formatAmount(booking.pricing.fees)}</span>
            </div>
          )}
          <div className="flex justify-between font-medium text-lg border-t pt-2">
            <span>Total:</span>
            <span>{formatAmount(booking.pricing.totalAmount)}</span>
          </div>
        </div>
      </div>



      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Submit Button (DPO only) */}
      <button
        type="button"
        onClick={initiateDpoPayment}
        disabled={isProcessing}
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isProcessing ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Processing...</span>
          </div>
        ) : (
          `Continue to DPO — ${formatAmount(booking.pricing.totalAmount)}`
        )}
      </button>

      {/* Security Notice */}
      <div className="text-xs text-gray-500 text-center">
        <div className="flex items-center justify-center space-x-1">
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <span>Your payment information is secure and encrypted</span>
        </div>
      </div>
    </form>
  );
}

export default PaymentFormContent;
