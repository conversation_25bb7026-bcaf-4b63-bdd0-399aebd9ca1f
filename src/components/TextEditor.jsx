'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import HtmlContentDisplay from './HtmlContentDisplay';

const TextEditor = ({
  value = '',
  onChange,
  placeholder = 'Start typing...',
  className = '',
  style = {},
  disabled = false,
  minHeight = '200px'
}) => {
  const [content, setContent] = useState(value);
  const [currentFont, setCurrentFont] = useState('Arial');
  const [currentColor, setCurrentColor] = useState('#000000');
  const [currentBackgroundColor, setCurrentBackgroundColor] = useState('transparent');
  const [isEmailLoading, setIsEmailLoading] = useState(false);
  const [isUrlLoading, setIsUrlLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const editorRef = useRef(null);
  const isUpdatingRef = useRef(false);

  // Handle client-side mounting to prevent hydration issues
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Initialize content when component mounts
  useEffect(() => {
    if (isMounted && editorRef.current && value && !editorRef.current.innerHTML) {
      editorRef.current.innerHTML = value;
      setContent(value);
    }
  }, [isMounted, value]);



  // Update content when value prop changes (but not during user editing)
  useEffect(() => {
    if (isMounted && value !== content && !isUpdatingRef.current && editorRef.current) {
      setContent(value);
      // Only update innerHTML if it's actually different to avoid cursor issues
      if (editorRef.current.innerHTML !== value) {
        let selection = null;
        let range = null;
        let cursorPosition = 0;

        // Safely get selection only on client side
        if (typeof window !== 'undefined') {
          selection = window.getSelection();
          range = selection && selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
          cursorPosition = range ? range.startOffset : 0;
        }

        editorRef.current.innerHTML = value;

        // Try to restore cursor position
        if (range && editorRef.current.firstChild) {
          try {
            const newRange = document.createRange();
            const textNode = editorRef.current.firstChild;
            const maxOffset = textNode.textContent ? Math.min(cursorPosition, textNode.textContent.length) : 0;
            newRange.setStart(textNode, maxOffset);
            newRange.setEnd(textNode, maxOffset);
            selection.removeAllRanges();
            selection.addRange(newRange);
          } catch (e) {
            // Ignore cursor restoration errors
          }
        }
      }
    }
  }, [value, content]);

  const handleContentChange = (e) => {
    if (isUpdatingRef.current) return;

    const newContent = e.target.innerHTML;
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }
  };

  // Helper function to trigger content updates
  const handleInput = () => {
    if (isUpdatingRef.current) return;

    const newContent = editorRef.current?.innerHTML || '';
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }
  };

  const applyFormat = (command, value = null) => {
    // Only run on client side to prevent hydration issues
    if (!isMounted || typeof window === 'undefined') return;

    // Save current selection
    const selection = window.getSelection();
    const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;

    if (range) {
      // Apply formatting to selected text
      document.execCommand(command, false, value);
    } else {
      // If no selection, focus and apply formatting for new text
      editorRef.current?.focus();
      document.execCommand(command, false, value);
    }

    // Update content after formatting
    isUpdatingRef.current = true;
    setTimeout(() => {
      const newContent = editorRef.current?.innerHTML || '';
      setContent(newContent);
      if (onChange) {
        onChange(newContent);
      }
      isUpdatingRef.current = false;
    }, 10);
  };

  // Unified function to apply CSS styles while preserving existing styles
  const applyStyleToSelection = (newStyles) => {
    // Only run on client side to prevent hydration issues
    if (!isMounted || typeof window === 'undefined') return;

    const selection = window.getSelection();

    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      if (!range.collapsed) {
        // Create a span element with the new styles
        const span = document.createElement('span');

        // Apply all the new styles
        Object.keys(newStyles).forEach(property => {
          span.style[property] = newStyles[property];
        });

        try {
          // Try to surround the contents with the span
          range.surroundContents(span);

          // Restore selection to the newly wrapped content
          const newRange = document.createRange();
          newRange.selectNodeContents(span);
          selection.removeAllRanges();
          selection.addRange(newRange);
          console.log('✅ Styles applied successfully via surroundContents');
          handleInput();

        } catch (error) {
          // If surroundContents fails (e.g., range spans multiple elements),
          // use a more robust approach
          try {
            const contents = range.extractContents();

            // Create a document fragment to hold our styled content
            const fragment = document.createDocumentFragment();

            // Clone the contents and apply styling
            const clonedContents = contents.cloneNode(true);
            const styledSpan = document.createElement('span');

            // Apply all the new styles
            Object.keys(newStyles).forEach(property => {
              styledSpan.style[property] = newStyles[property];
            });

            styledSpan.appendChild(clonedContents);
            fragment.appendChild(styledSpan);

            // Insert the styled content
            range.insertNode(fragment);

            // Select the newly inserted content
            const newRange = document.createRange();
            newRange.selectNodeContents(styledSpan);
            selection.removeAllRanges();
            selection.addRange(newRange);
            console.log('✅ Styles applied successfully via DOM manipulation');
            handleInput();

          } catch (fallbackError) {
            console.warn('Style application failed:', fallbackError);
            // Last resort: create a new span and insert it
            const fallbackSpan = document.createElement('span');

            // Apply all the new styles
            Object.keys(newStyles).forEach(property => {
              fallbackSpan.style[property] = newStyles[property];
            });

            fallbackSpan.textContent = selection.toString();

            try {
              range.deleteContents();
              range.insertNode(fallbackSpan);

              // Select the newly inserted span
              const newRange = document.createRange();
              newRange.selectNodeContents(fallbackSpan);
              selection.removeAllRanges();
              selection.addRange(newRange);
            } catch (finalError) {
              console.error('All style application methods failed:', finalError);
            }
          }
        }
      } else {
        // No text selected - set styles for future typing
        editorRef.current?.focus();

        // Create a temporary span at cursor position for future typing
        const span = document.createElement('span');

        // Apply all the new styles
        Object.keys(newStyles).forEach(property => {
          span.style[property] = newStyles[property];
        });

        span.innerHTML = '&#8203;'; // Zero-width space to maintain cursor position

        try {
          range.insertNode(span);

          // Position cursor after the span
          const newRange = document.createRange();
          newRange.setStartAfter(span);
          newRange.setEndAfter(span);
          selection.removeAllRanges();
          selection.addRange(newRange);
        } catch (error) {
          console.warn('Could not set styles for future typing:', error);
        }
      }
    } else {
      // No selection at all - just focus the editor
      editorRef.current?.focus();
    }

    // Update content immediately
    isUpdatingRef.current = true;
    const newContent = editorRef.current?.innerHTML || '';
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }

    // Reset the updating flag after a brief delay
    setTimeout(() => {
      isUpdatingRef.current = false;
    }, 10);
  };

  const handleFontChange = (e) => {
    const font = e.target.value;
    setCurrentFont(font);
    applyFormat('fontName', font);
  };

  const handleColorChange = (e) => {
    const color = e.target.value;
    setCurrentColor(color);
    applyFormat('foreColor', color);
  };

  const handleBackgroundColorChange = (e) => {
    const backgroundColor = e.target.value;
    setCurrentBackgroundColor(backgroundColor);

    // Use the unified styling function to apply background color
    applyStyleToSelection({ backgroundColor });
  };

  const handleRemoveBackgroundColor = () => {
    console.log('🗑️ Remove background color clicked');
    setCurrentBackgroundColor('transparent');

    // Only run on client side to prevent hydration issues
    if (!isMounted || typeof window === 'undefined') return;

    const selection = window.getSelection();
    console.log('Selection range count:', selection.rangeCount);

    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      console.log('Range collapsed:', range.collapsed);

      if (!range.collapsed) {
        try {
          // Get the selected text content
          const selectedText = range.toString();
          console.log('Selected text:', selectedText);

          // Get the HTML content of the selection
          const selectedHTML = range.cloneContents();
          const tempDiv = document.createElement('div');
          tempDiv.appendChild(selectedHTML);
          console.log('Selected HTML before:', tempDiv.innerHTML);

          // Delete the current selection
          range.deleteContents();

          // Create a new text node without any styling
          const textNode = document.createTextNode(selectedText);

          // Insert the plain text node
          range.insertNode(textNode);

          // Clear the selection
          selection.removeAllRanges();

          // Update the content
          handleInput();

          console.log('✅ Background color removed successfully');

        } catch (error) {
          console.warn('Error removing background color:', error);
          // Fallback: try to set background to empty string
          applyStyleToSelection({ backgroundColor: '' });
        }
      } else {
        console.log('No text selected');
      }
    } else {
      console.log('No selection range found');
    }
  };





  const handleEmailLink = async () => {
    const selection = window.getSelection();
    if (!selection.rangeCount || selection.toString().trim() === '') {
      alert('Please select text to convert to an email link.');
      return;
    }

    const selectedText = selection.toString().trim();
    setIsEmailLoading(true);

    try {
      // Simulate processing time for user feedback
      await new Promise(resolve => setTimeout(resolve, 500));

      const range = selection.getRangeAt(0);
      const emailLink = document.createElement('a');
      emailLink.href = `mailto:${selectedText}`;
      emailLink.textContent = selectedText;
      emailLink.style.color = 'white';
      emailLink.style.textDecoration = 'underline';

      range.deleteContents();
      range.insertNode(emailLink);

      // Clear selection
      selection.removeAllRanges();

      // Update content
      isUpdatingRef.current = true;
      const newContent = editorRef.current?.innerHTML || '';
      setContent(newContent);
      if (onChange) {
        onChange(newContent);
      }

      setTimeout(() => {
        isUpdatingRef.current = false;
      }, 10);

    } catch (error) {
      console.error('Error creating email link:', error);
      alert('Failed to create email link. Please try again.');
    } finally {
      setIsEmailLoading(false);
    }
  };

  const handleUrlLink = async () => {
    const selection = window.getSelection();
    if (!selection.rangeCount || selection.toString().trim() === '') {
      alert('Please select text to convert to a URL link.');
      return;
    }

    const selectedText = selection.toString().trim();

    // Prompt user for the URL
    const userUrl = prompt('Enter the URL to link to:');

    // Check if user cancelled or entered empty URL
    if (userUrl === null || userUrl.trim() === '') {
      return; // Abort operation
    }

    let url = userUrl.trim();

    // Add protocol if missing
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = `https://${url}`;
    }

    setIsUrlLoading(true);

    try {
      // Simulate processing time for user feedback
      await new Promise(resolve => setTimeout(resolve, 500));

      const range = selection.getRangeAt(0);

      const urlLink = document.createElement('a');
      urlLink.href = url;
      urlLink.textContent = selectedText; // Keep selected text as display text
      urlLink.target = '_blank';
      urlLink.rel = 'noopener noreferrer';
      urlLink.style.color = 'white';
      urlLink.style.textDecoration = 'underline';

      range.deleteContents();
      range.insertNode(urlLink);

      // Clear selection
      selection.removeAllRanges();

      // Update content
      isUpdatingRef.current = true;
      const newContent = editorRef.current?.innerHTML || '';
      setContent(newContent);
      if (onChange) {
        onChange(newContent);
      }

      setTimeout(() => {
        isUpdatingRef.current = false;
      }, 10);

    } catch (error) {
      console.error('Error creating URL link:', error);
      alert('Failed to create URL link. Please try again.');
    } finally {
      setIsUrlLoading(false);
    }
  };

  const handleSelectionChange = useCallback(() => {
    // Only run on client side to prevent hydration issues
    if (!isMounted || typeof window === 'undefined') return;

    const selection = window.getSelection();
    if (selection && selection.toString()) {
      // Try to detect current formatting of selected text
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let parentElement = range.commonAncestorContainer;

        // If it's a text node, get its parent element
        if (parentElement.nodeType === Node.TEXT_NODE) {
          parentElement = parentElement.parentElement;
        }

        // Reset to defaults first
        let detectedFontSize = null;
        let detectedLineHeight = null;

        // Look for the closest element with font-size and line-height styles
        let currentElement = parentElement;
        while (currentElement && currentElement !== editorRef.current) {
          // Check inline styles first (highest priority)
          if (currentElement.style && currentElement.style.fontSize && !detectedFontSize) {
            const fontSize = currentElement.style.fontSize;
            const predefinedSizes = ['20px', '28px', '40px', '60px'];
            if (predefinedSizes.includes(fontSize)) {
              detectedFontSize = fontSize;
              console.log('✅ Found inline font size:', fontSize);
            }
          }

          if (currentElement.style && currentElement.style.lineHeight && !detectedLineHeight) {
            const lineHeight = currentElement.style.lineHeight;
            const predefinedLineHeights = ['0.4', '0.6', '0.8', '1.0', '1.2', '1.4', '1.6', '1.8', '2.0'];
            if (predefinedLineHeights.includes(lineHeight)) {
              detectedLineHeight = lineHeight;
              console.log('✅ Found inline line height:', lineHeight);
            }
          }

          // Also check computed style if we haven't found inline styles
          if (!detectedFontSize || !detectedLineHeight) {
            const computedStyle = window.getComputedStyle(currentElement);

            if (!detectedFontSize && computedStyle.fontSize && computedStyle.fontSize !== 'inherit') {
              const fontSize = computedStyle.fontSize;
              const predefinedSizes = ['20px', '28px', '40px', '60px'];
              if (predefinedSizes.includes(fontSize)) {
                detectedFontSize = fontSize;
                console.log('✅ Found computed font size:', fontSize);
              }
            }

            if (!detectedLineHeight && computedStyle.lineHeight && computedStyle.lineHeight !== 'normal') {
              const lineHeight = computedStyle.lineHeight;
              const predefinedLineHeights = ['0.4', '0.6', '0.8', '1.0', '1.2', '1.4', '1.6', '1.8', '2.0'];
              if (predefinedLineHeights.includes(lineHeight)) {
                detectedLineHeight = lineHeight;
                console.log('✅ Found computed line height:', lineHeight);
              }
            }
          }

          currentElement = currentElement.parentElement;
        }

        // Update the state with detected values
        // Note: Font size and line height detection removed for simplicity
      }
    }
  }, [isMounted]);

  return (
    <div className={`text-editor-container ${className}`} style={style}>
      {/* Editor Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-3">
          Rich Text Editor
        </h3>

        <div className="border border-gray-300 rounded-md overflow-hidden">
          {/* Custom Toolbar */}
          <div className="bg-gray-50 border-b border-gray-300 p-2 flex items-center gap-2 flex-wrap">
            {/* Bold Button */}
            <button
              type="button"
              onClick={() => applyFormat('bold')}
              className="px-3 py-1 bg-white border border-gray-300 rounded hover:bg-gray-100 font-bold"
              disabled={disabled}
              title="Bold (Ctrl+B)"
            >
              B
            </button>

            {/* Italic Button */}
            <button
              type="button"
              onClick={() => applyFormat('italic')}
              className="px-3 py-1 bg-white border border-gray-300 rounded hover:bg-gray-100 italic"
              disabled={disabled}
              title="Italic (Ctrl+I)"
            >
              I
            </button>

            {/* Font Family Selector */}
            <select
              value={currentFont}
              onChange={handleFontChange}
              disabled={disabled}
              className="px-2 py-1 bg-white border border-gray-300 rounded text-sm"
              title="Font Family"
            >
              <option value="Arial">Arial</option>
              <option value="Times New Roman">Times New Roman</option>
              <option value="Helvetica">Helvetica</option>
              <option value="Georgia">Georgia</option>
              <option value="Verdana">Verdana</option>
            </select>

            {/* Font Size Selector */}
            {/* <select
              value={currentFontSize}
              onChange={handleFontSizeChange}
              disabled={disabled}
              className="px-2 py-1 bg-white border border-gray-300 rounded text-sm"
              title="Font Size"
            >
              <option value="60px">Large (60px)</option>
              <option value="40px">Medium (40px)</option>
              <option value="28px">Small (28px)</option>
              <option value="20px">Extra Small (20px)</option>
            </select> */}

            {/* Line Height Selector */}
            {/* <div className="flex items-center gap-1">
              <label className="text-sm text-gray-600">Line:</label>
              <select
                value={currentLineHeight}
                onChange={handleLineHeightChange}
                disabled={disabled}
                className="px-2 py-1 bg-white border border-gray-300 rounded text-sm"
                title="Line Height"
              >
                <option value="0.4">0.4</option>
                <option value="0.6">0.6</option>
                <option value="0.8">0.8</option>
                <option value="1.0">1.0</option>
                <option value="1.2">1.2</option>
                <option value="1.4">1.4</option>
                <option value="1.6">1.6</option>
                <option value="1.8">1.8</option>
                <option value="2.0">2.0</option>
              </select>
            </div> */}

            {/* Color Picker */}
            <div className="flex items-center gap-1">
              <label htmlFor="color-picker" className="text-sm text-gray-600">Color:</label>
              <input
                id="color-picker"
                type="color"
                value={currentColor}
                onChange={handleColorChange}
                disabled={disabled}
                className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
                title="Text Color"
              />
            </div>

            {/* Background Color Picker */}
            <div className="flex items-center gap-1">
              <label htmlFor="bg-color-picker" className="text-sm text-gray-600">Highlight:</label>
              <input
                id="bg-color-picker"
                type="color"
                value={currentBackgroundColor === 'transparent' ? '#ffff00' : currentBackgroundColor}
                onChange={handleBackgroundColorChange}
                disabled={disabled}
                className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
                title="Background/Highlight Color"
              />
              <button
                type="button"
                onClick={handleRemoveBackgroundColor}
                disabled={disabled}
                className="px-2 py-1 bg-white border border-gray-300 rounded hover:bg-gray-100 text-xs"
                title="Remove background color"
              >
                None
              </button>
            </div>

            {/* Separator */}
            <div className="w-px h-6 bg-gray-300 mx-1" />

            {/* Email Link Button */}
            <button
              type="button"
              onClick={handleEmailLink}
              disabled={disabled || isEmailLoading}
              className="px-3 py-1 bg-blue-600 text-white border border-blue-600 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
              title="Convert selected text to email link"
            >
              {isEmailLoading ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent"></div>
                  <span className="text-xs">Email...</span>
                </>
              ) : (
                <span className="text-xs">📧 Email</span>
              )}
            </button>

            {/* URL Link Button */}
            <button
              type="button"
              onClick={handleUrlLink}
              disabled={disabled || isUrlLoading}
              className="px-3 py-1 bg-green-600 text-white border border-green-600 rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
              title="Convert selected text to custom URL link (you'll be prompted for the URL)"
            >
              {isUrlLoading ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent"></div>
                  <span className="text-xs">URL...</span>
                </>
              ) : (
                <span className="text-xs">🔗 URL</span>
              )}
            </button>
          </div>

          {/* Editor Content */}
          {isMounted ? (
            <div
              ref={editorRef}
              contentEditable={!disabled}
              onInput={handleContentChange}
              onMouseUp={handleSelectionChange}
              onKeyUp={handleSelectionChange}
              onFocus={handleSelectionChange}
              onBlur={() => {/* Selection cleared on blur */}}
              className="p-4 bg-black text-white placeholder:text-gray-500 focus:outline-none"
              style={{ minHeight }}
              data-placeholder={placeholder}
              suppressContentEditableWarning={true}
            />
          ) : (
            <div
              className="p-4 bg-black text-white placeholder:text-gray-500"
              style={{ minHeight }}
            >
              {/* Loading placeholder to prevent hydration mismatch */}
            </div>
          )}
        </div>
      </div>

      {/* Preview Section */}
      <div className="preview-section">
        <h3 className="text-lg font-medium text-gray-800 mb-3">
          Formatted Preview
        </h3>
        <div className="border border-gray-200 bg-black rounded-md p-4 min-h-[150px] 'bg-gray-50">
          {content ? (
            <HtmlContentDisplay htmlString={content} />
          ) : (
            <p className="text-gray-500 italic">
              Your formatted text will appear here...
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default TextEditor;