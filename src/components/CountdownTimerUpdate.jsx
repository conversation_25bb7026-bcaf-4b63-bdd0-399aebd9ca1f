'use client'
import React, { useState, useEffect } from 'react';
import ImageWrapperResponsive from './ImageWrapperResponsive';

const CountdownTimer = ({ targetDate = new Date('2025-09-30T00:00:00') }) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    isExpired: false
  });

  const cssText = 'text-2xl md:text-4xl mb-0';
  const cssTextWrap = "flex flex-col items-center bg-black/50 backdrop-blur-sm rounded-lg p-1 md:p-2 min-w-[40px] md:min-w-[80px]";
  const cssTextSmall = "text-[8px] md:text-[10px] opacity-80";

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const target = new Date(targetDate).getTime();
      const difference = target - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({
          days,
          hours,
          minutes,
          seconds,
          isExpired: false
        });
      } else {
        setTimeLeft({
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          isExpired: true
        });
      }
    };

    // Calculate immediately
    calculateTimeLeft();

    // Set up interval to update every second
    const timer = setInterval(calculateTimeLeft, 1000);

    // Cleanup interval on component unmount
    return () => clearInterval(timer);
  }, [targetDate]);

  const formatNumber = (num) => {
    return num.toString().padStart(2, '0');
  };

  if (timeLeft.isExpired) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white px-4">
        <div className="text-center">
          <div className="mb-8">
            <div className="w-24 h-24 mx-auto mb-4 bg-white rounded-full flex items-center justify-center">
              <svg className="w-16 h-16 text-blue-800" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-2">ELEPHANT</h1>
            <h2 className="text-2xl md:text-3xl font-light tracking-wider">ISLAND</h2>
          </div>
          <div className="text-xl md:text-2xl font-semibold mb-4">
            WEBSITE IS NOW LIVE!
          </div>
          <p className="text-lg opacity-90">
            Welcome to Elephant Island
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute z-10 bg-black/30 text-white w-full h-full flex flex-col items-center justify-center">
      <div className="text-center gap-4">
        {/* Logo Section */}
        <div className="md:flex hidden mb-1">
          <ImageWrapperResponsive src={'/assets/elephant_island_logo_001.png'}/>
        </div>
        <div className="md:hidden flex mb-1">
          <ImageWrapperResponsive src={'/assets/mobile_elephant_island_logo.png'}/>
        </div>

        {/* Coming Soon Text */}
        <div className="mb-1">
          <div className="flex items-center justify-center gap-2 text-lg md:text-xl">
            <div>-</div>
            WEBSITE
            <div>-</div>
          </div>
          <h3 className="text-2xl md:text-4xl font-bold">COMING SOON</h3>
        </div>

        {/* Countdown Display */}
        <div className="flex flex-wrap justify-center gap-1 md:gap-2">
          {/* Days */}
          <div className={cssTextWrap}>
            <div className={cssText}>
              {formatNumber(timeLeft.days)}
            </div>
            <div className={cssTextSmall}>
              DAYS
            </div>
          </div>

          {/* Hours */}
          <div className={cssTextWrap}>
            <div className={cssText}>
              {formatNumber(timeLeft.hours)}
            </div>
            <div className={cssTextSmall}>
              HOURS
            </div>
          </div>

          {/* Minutes */}
          <div className={cssTextWrap}>
            <div className={cssText}>
              {formatNumber(timeLeft.minutes)}
            </div>
            <div className={cssTextSmall}>
              MINS
            </div>
          </div>

          {/* Seconds */}
          <div className={cssTextWrap}>
            <div className={cssText}>
              {formatNumber(timeLeft.seconds)}
            </div>
            <div className={cssTextSmall}>
              SECS
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CountdownTimer;
