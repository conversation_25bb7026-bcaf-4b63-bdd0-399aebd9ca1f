'use client'
import React, { useCallback, useEffect, useState, useRef } from 'react'
import HtmlContentDisplay from './HtmlContentDisplay';
import TextEditor from './TextEditor';
import { uploadFile, deleteFileByUrl } from '@/lib/file-upload';

const cssInputWrapper=`flex w-full flex-col h-fit gap-2 mx-auto`  
const ccsInputSection='bg-black/50 rounded-md p-2'  

function InputWrapper({
    formData,
    handleInputChange,
    errors,
    editingIndex,
    onImageSelect,
    onImageRemove,
    isUploading,
    uploadProgress,
    imagePreview,
    fileInputRef
}) {
    // Get current image URL from the form data passed in
    const currentImageUrl = formData.image;

    return(
        <div className='flex flex-col w-full h-fit'>
            <div className={cssInputWrapper}>
                <label className='text-xl pt-5 capitalize' htmlFor="title">
                    enter the title input here
                    {editingIndex !== null && (
                        <span className='text-sm text-blue-600 ml-2 font-normal'>
                            (Editing item #{editingIndex + 1})
                        </span>
                    )}
                </label>
                <div className='flex flex-col w-full p-2 gap-2 rounded-md'>
                    <div className={ccsInputSection}>
                        <TextEditor
                            value={formData.title || ''}
                            onChange={(content) => handleInputChange('title', content)}
                            placeholder='Enter title with rich formatting...'
                            minHeight='80px'
                            className={`w-full ${errors?.title ? 'border-red-500' : ''}`}
                        />
                    </div>
                    {errors?.title && (
                        <p className="text-sm text-red-600 mt-1">{errors.title}</p>
                    )}
                </div>
                <div className={cssInputWrapper}>
                    <label className='text-xl pt-3 capitalize' htmlFor="body1">enter the body1 input here</label>
                    <div className='flex flex-col w-full px-2 gap-2 rounded-md'>
                        <div className={ccsInputSection}>
                            <TextEditor
                                value={formData.body1 || ''}
                                onChange={(content) => handleInputChange('body1', content)}
                                placeholder='Enter body content with rich formatting...'
                                minHeight='150px'
                                className={`w-full ${errors?.body1 ? 'border-red-500' : ''}`}
                            />
                        </div>
                        {errors?.body1 && (
                            <p className="text-sm text-red-600 mt-1">{errors.body1}</p>
                        )}
                    </div>
                </div>
                {/* Image Upload Feature */}
                <div className={cssInputWrapper}>
                    <label className='text-xl pt-5 capitalize'>
                        add thumbnail
                        {editingIndex !== null && (
                            <span className='text-sm text-blue-600 ml-2 font-normal'>
                                (Editing item #{editingIndex + 1})
                            </span>
                        )}
                    </label>
                    <div className='flex flex-col w-full bg-black/75 text-white py-4 px-4 gap-4 rounded-md'>

                        {/* Current Image Display */}
                        {currentImageUrl && !imagePreview && (
                            <div className="relative">
                                <img
                                    src={currentImageUrl}
                                    alt="Current thumbnail"
                                    className="max-w-xs h-32 object-cover rounded-md border border-gray-300"
                                />
                                <button
                                    type="button"
                                    onClick={onImageRemove}
                                    disabled={isUploading}
                                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 disabled:opacity-50"
                                >
                                    ×
                                </button>
                            </div>
                        )}

                        {/* Image Preview */}
                        {imagePreview && (
                            <div className="relative">
                                <img
                                    src={imagePreview}
                                    alt="Preview"
                                    className="max-w-xs h-32 object-cover rounded-md border border-gray-300"
                                />
                                <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                                    ↑
                                </div>
                            </div>
                        )}

                        {/* File Input */}
                        <div className="flex flex-col gap-2">
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept="image/jpeg,image/jpg,image/png,image/webp"
                                onChange={onImageSelect}
                                disabled={isUploading}
                                className="block w-full text-sm text-gray-300 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-600 file:text-white hover:file:bg-blue-700 disabled:opacity-50"
                            />
                            <p className="text-xs text-gray-400">
                                Supported formats: JPEG, PNG, WebP (max 5MB)
                            </p>
                        </div>

                        {/* Upload Progress */}
                        {isUploading && uploadProgress > 0 && (
                            <div className="w-full bg-gray-600 rounded-full h-2">
                                <div
                                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${uploadProgress}%` }}
                                ></div>
                            </div>
                        )}

                        {/* Upload Status */}
                        {isUploading && (
                            <p className="text-sm text-blue-400">
                                {uploadProgress < 100 ? 'Uploading...' : 'Upload complete!'}
                            </p>
                        )}
                    </div>

                    {/* Error Display */}
                    {errors?.image && (
                        <p className="text-sm text-red-600 mt-1">{errors.image}</p>
                    )}
                </div>
            </div>
        </div>
    )
}

export default function IslandAndInfoMarkerInput({ pages, onSave, isLoading = false, section = 'island', heading }) {
  // Initialize main island form data from pages prop
  const [mainFormData, setMainFormData] = useState({
    title: '',
    body1: '',
    image: '',
  });

  // Separate form data for editing additional content items
  const [editFormData, setEditFormData] = useState({
    title: '',
    body1: '',
    image: '',
  });

  const [additionalFormData, setAdditionalFormData] = useState([]);
  const [editingIndex, setEditingIndex] = useState(null); // Tracks the index of the item being edited
  const [errors, setErrors] = useState({});

  // Image upload states
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showReplaceDialog, setShowReplaceDialog] = useState(false);
  const [pendingFile, setPendingFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const fileInputRef = useRef(null);
  const hasInitializedRef = useRef(false);

  // Initialize form data from pages prop (guarded to avoid clobbering local edits)
  useEffect(() => {
    const sectionData = pages && section ? pages[section] : null;
    if (!sectionData) return;

    // Initialize only on first load, or when there is no local data and not editing
    if (!hasInitializedRef.current || (editingIndex === null && additionalFormData.length === 0)) {
      setMainFormData({
        title: sectionData.title || '',
        body1: sectionData.body1 || '',
        image: sectionData.image || '',
      });
      setAdditionalFormData(sectionData.additionalContent || []);
      hasInitializedRef.current = true;
    }
  }, [pages, section, editingIndex, additionalFormData.length]);

  // Update image preview when editing different items
  useEffect(() => {
    if (editingIndex !== null && additionalFormData[editingIndex]) {
      // Clear any existing preview when switching to edit mode
      setImagePreview('');
    } else if (editingIndex === null) {
      // Clear preview when switching back to main form
      setImagePreview('');
    }
  }, [editingIndex, additionalFormData]);


  
  // Get current form data based on editing state
  const getCurrentFormData = useCallback(() => {
    return editingIndex !== null ? editFormData : mainFormData;
  }, [editingIndex, editFormData, mainFormData]);

  // Validation
  const validateForm = useCallback(() => {
    const newErrors = {};
    const currentData = getCurrentFormData();

    if (!currentData.title?.trim()) {
      newErrors['title'] = editingIndex !== null ? 'Additional content title is required' : 'Island title is required';
    }
    if (!currentData.body1?.trim()) {
      newErrors['body1'] = editingIndex !== null ? 'Additional content body is required' : 'Island body1 is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [getCurrentFormData, editingIndex]);

  const handleInputChange = useCallback((field, value) => {
    if (editingIndex !== null) {
      // Update edit form data when editing additional content
      setEditFormData(prev => ({
        ...prev,
        [field]: value
      }));
    } else {
      // Update main form data when editing main island content
      setMainFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  }, [editingIndex, errors]);
      
  // Handle section save
  const handleSectionSave = useCallback(async () => {
    if (editingIndex !== null) {
      // We shouldn't save main form while editing additional content
      return;
    }

    if (!validateForm()) {
      return;
    }

    try {
      const islandData = {
        title: mainFormData.title,
        body1: mainFormData.body1,
        image: mainFormData.image,
        additionalContent: additionalFormData
      };

      await onSave({ [section]: islandData });
    } catch (error) {
      console.error('Save error:', error);
    }
  }, [mainFormData, additionalFormData, validateForm, onSave, editingIndex]);

  const handleAdditionalClick = () => {
    if(editingIndex !== null) {
      // Validate edit form data before saving
      if (!validateForm()) {
        return;
      }

      // Logic for editing an existing item
      const updatedContent = [...additionalFormData];
      const updatedItem = {
        title: editFormData.title,
        body1: editFormData.body1,
        image: editFormData.image
      };

      updatedContent[editingIndex] = updatedItem;

      setAdditionalFormData(updatedContent);
      setEditingIndex(null); // Exit edit mode
      setEditFormData({ title: '', body1: '', image: '' }); // Clear edit form
    } else {
      // Validate main form data before adding
      if (!validateForm()) {
        return;
      }

      // Logic for adding a new item
      setAdditionalFormData([...additionalFormData, {
        title: mainFormData.title,
        body1: mainFormData.body1,
        image: mainFormData.image
      }]);
      setMainFormData({ title: '', body1: '', image: '' }); // Clear main form after adding
    }
  }

  const handleCancelEdit = () => {
    setEditingIndex(null); // Exit edit mode
    setEditFormData({ title: '', body1: '', image: '' }); // Clear edit form
  }

  // Image upload functions
  const validateImageFile = (file) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Please select a JPEG, PNG, or WebP image.');
    }

    if (file.size > maxSize) {
      throw new Error('File too large. Maximum size is 5MB.');
    }

    return true;
  };

  const handleImageSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      validateImageFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => setImagePreview(e.target.result);
      reader.readAsDataURL(file);

      // Check if there's an existing image
      const currentImage = editingIndex !== null
        ? editFormData.image
        : mainFormData.image;

      if (currentImage && currentImage.trim()) {
        // Show replacement dialog
        setPendingFile(file);
        setShowReplaceDialog(true);
      } else {
        // Upload directly
        await uploadImage(file);
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, image: error.message }));
    }
  };

  const uploadImage = async (file) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const result = await uploadFile(file, 'pages');

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (result.success) {
        // Update the appropriate form data
        if (editingIndex !== null) {
          // Update edit form data (this is what the user sees and what gets saved)
          setEditFormData(prev => ({
            ...prev,
            image: result.url
          }));
        } else {
          // Update main form data
          handleInputChange('image', result.url);
        }

        // Clear upload states
        setTimeout(() => {
          setUploadProgress(0);
          setIsUploading(false);
          setImagePreview('');
        }, 1000);
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, image: error.message }));
      setIsUploading(false);
      setUploadProgress(0);
      setImagePreview('');
    }
  };

  const handleReplaceConfirm = async () => {
    if (!pendingFile) return;

    try {
      // Get the existing image URL
      const existingImage = editingIndex !== null
        ? editFormData.image
        : mainFormData.image;

      // Delete existing image if it's a Firebase URL
      if (existingImage && existingImage.includes('firebasestorage.googleapis.com')) {
        try {
          await deleteFileByUrl(existingImage);
        } catch (deleteError) {
          console.warn('Failed to delete existing image:', deleteError);
          // Continue with upload even if delete fails
        }
      }

      // Upload new image
      await uploadImage(pendingFile);

      // Clean up
      setShowReplaceDialog(false);
      setPendingFile(null);
    } catch (error) {
      setErrors(prev => ({ ...prev, image: error.message }));
      setShowReplaceDialog(false);
      setPendingFile(null);
    }
  };

  const handleReplaceCancel = () => {
    setShowReplaceDialog(false);
    setPendingFile(null);
    setImagePreview('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleImageRemove = async () => {
    const currentImage = editingIndex !== null
      ? editFormData.image
      : mainFormData.image;

    if (currentImage && currentImage.includes('firebasestorage.googleapis.com')) {
      try {
        await deleteFileByUrl(currentImage);
      } catch (error) {
        console.warn('Failed to delete image from Firebase:', error);
      }
    }

    // Clear image from form data
    if (editingIndex !== null) {
      // Update edit form data (this is what the user sees and what gets saved)
      setEditFormData(prev => ({
        ...prev,
        image: ''
      }));
    } else {
      handleInputChange('image', '');
    }

    // Clear file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
      const handleDeleteAdditional = (index) => {
          // If we're deleting the item currently being edited, exit edit mode
          if (editingIndex === index) {
              setEditingIndex(null);
              setEditFormData({ title: '', body1: '', image: '' });
          } else if (editingIndex !== null && editingIndex > index) {
              // If we're deleting an item before the one being edited, adjust the editing index
              setEditingIndex(editingIndex - 1);
          }

          const updatedContent = additionalFormData.filter((_, i) => i !== index);
          setAdditionalFormData(updatedContent);
      };

      const handleEditAdditional = (index) => {
          setEditFormData(additionalFormData[index]); // Populate edit form with data from the selected item
          setEditingIndex(index); // Set the editing index
      };
      
  return (
    <div className='flex bg-gray-100 w-full h-full flex-col px-12 py-10 overflow-hidden'>
      <div className="flex items-center justify-between mb-6">
        <h1 className='text-3xl'>{heading || `${section === 'island' ? 'Island' : section.charAt(0).toUpperCase() + section.slice(1)} Page Content`}</h1>
        <button
          type="button"
          onClick={handleSectionSave}
          disabled={isLoading}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Saving...' : `Save ${section === 'island' ? 'Island' : section.charAt(0).toUpperCase() + section.slice(1)} Section`}
        </button>
      </div>
          
          <div className='w-full h-full overflow-y-auto mb-10'>
              {/* SHOWING THE FORMAT-DATA OBJECT ENTRIES */}
              <div className='px-2 gap-5 h-fit bg-black w-full rounded-md'>
                  <div className='flex flex-col gap-2 px-2 text-white mr-2 shadow rounded-md'>
                      <div className='flex mt-2 w-full gap-5 flex-row-reverse items-start'>
                          <div className='flex mt-2 flex-col gap-2 flex-3/5'>
                              <div className='text-2xl capitalize'>
                                  {/* {mainFormData?.title} */}
                                  <HtmlContentDisplay htmlString={mainFormData.title} />
                              </div>
                              {/* <h1 className='text-2xl capitalize'>{mainFormData?.title}</h1> */}
                              <div><HtmlContentDisplay htmlString={mainFormData?.body1} /></div>
                          </div>
                          {mainFormData?.image ? (
                              <img className='w-60 mt-2 aspect-auto' src={mainFormData.image} alt="Thumbnail" />
                            ) : null}
                      </div>
                  </div>
                  
                  {/* DISPLAYING ADDITIONAL CONTENT */}
                  <div className='flex h-fit flex-col w-full text-white'>
                      {additionalFormData?.length > 0 &&
                          <div className='text-center mt-5 mb-2 text-gray-400 capitalize'>Additional Content</div>
                      }
                      {additionalFormData?.map((item, index) => (
                          <div
                              key={`${index}-${item.title}-${item.body1}-${item.image}`}
                              className={`p-4 border-b border-gray-700 last:border-0 flex flex-col ${
                                  editingIndex === index
                                      ? 'bg-blue-900/30 border-blue-500 border-2 rounded-md mb-2'
                                      : ''
                              }`}
                          >
                              {editingIndex === index && (
                                  <div className='text-xs text-blue-400 mb-2 font-medium'>
                                      Currently editing this item
                                  </div>
                              )}
                              <div className='flex mt-2 flex-row-reverse items-start gap-4'>
                                <div className='flex mt-2 flex-col gap-2 flex-3/5'>
                                  <div >
                                      <HtmlContentDisplay htmlString={item.title} />
                                  </div>

                                  {/* <h1 className='text-xl capitalize'>{item.title}</h1> */}
                                  {/* <p>{item.body1}</p> */}
                                  <HtmlContentDisplay htmlString={item.body1} />
                                </div>
                                {item?.image ? (
                                  <img className='w-32 mt-2 aspect-auto' src={item.image} alt="Thumbnail" />
                                ) : null}
                              </div>
                              <div className='flex mt-2 gap-2'>
                                  <button
                                      onClick={() => handleEditAdditional(index)}
                                      disabled={editingIndex === index}
                                      className={`text-white capitalize cursor-pointer h-8 px-3 rounded-md ${
                                          editingIndex === index
                                              ? 'bg-gray-500 cursor-not-allowed'
                                              : 'bg-blue-600 hover:bg-blue-700'
                                      }`}
                                  >
                                      {editingIndex === index ? 'Editing...' : 'Edit'}
                                  </button>
                                  <button
                                      onClick={() => handleDeleteAdditional(index)}
                                      disabled={editingIndex === index}
                                      className={`text-white capitalize cursor-pointer h-8 px-3 rounded-md ${
                                          editingIndex === index
                                              ? 'bg-gray-500 cursor-not-allowed'
                                              : 'bg-red-600 hover:bg-red-700'
                                      }`}
                                  >
                                      Delete
                                  </button>
                              </div>
                          </div>
                      ))}
                  </div>
              </div>
              
              {/* MAIN INPUT SECTION */}
              <InputWrapper
                  formData={getCurrentFormData()}
                  handleInputChange={handleInputChange}
                  errors={errors}
                  editingIndex={editingIndex}
                  onImageSelect={handleImageSelect}
                  onImageRemove={handleImageRemove}
                  isUploading={isUploading}
                  uploadProgress={uploadProgress}
                  imagePreview={imagePreview}
                  fileInputRef={fileInputRef}
              />
              
              <div className='flex w-full mt-2 justify-between items-center'>
                  <div>
                      {editingIndex !== null && (
                          <div className='text-sm text-blue-600 font-medium'>
                              Editing additional content item #{editingIndex + 1}
                          </div>
                      )}
                  </div>
                  <div className='flex mr-4 gap-2'>
                      {editingIndex !== null && (
                          <button
                              onClick={handleCancelEdit}
                              className='text-white capitalize cursor-pointer h-10 w-fit text-xs px-4 bg-red-600 rounded-md shadow hover:bg-red-700'
                          >
                              Cancel Edit
                          </button>
                      )}
                      <button
                          onClick={handleAdditionalClick}
                          className={`text-white capitalize cursor-pointer h-10 w-fit text-xs px-4 rounded-md shadow ${
                              editingIndex !== null
                                  ? 'bg-blue-600 hover:bg-blue-700'
                                  : 'bg-gray-700 hover:bg-gray-800'
                          }`}
                      >
                          {editingIndex !== null ? 'Save Changes' : 'Add Additional Content'}
                      </button>
                  </div>
              </div>
          </div>

          {/* Image Replacement Confirmation Dialog */}
          {showReplaceDialog && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">
                    Replace Existing Image
                  </h3>
                  <button
                    onClick={handleReplaceCancel}
                    disabled={isUploading}
                    className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
                  >
                    ×
                  </button>
                </div>

                {/* Content */}
                <div className="p-6 space-y-4">
                  <div className="bg-amber-50 border border-amber-200 rounded-md p-4">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-amber-800">
                          An image already exists
                        </p>
                        <p className="text-sm text-amber-700 mt-1">
                          Do you want to replace the existing image with the new one? The old image will be permanently deleted.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Preview of new image */}
                  {imagePreview && (
                    <div className="text-center">
                      <p className="text-sm text-gray-600 mb-2">New image preview:</p>
                      <img
                        src={imagePreview}
                        alt="New image preview"
                        className="max-w-full h-32 object-cover rounded-md border border-gray-300 mx-auto"
                      />
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
                  <button
                    onClick={handleReplaceCancel}
                    disabled={isUploading}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleReplaceConfirm}
                    disabled={isUploading}
                    className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50"
                  >
                    {isUploading ? 'Replacing...' : 'Replace Image'}
                  </button>
                </div>
              </div>
            </div>
          )}
      </div>
    )
}