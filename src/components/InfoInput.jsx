'use client'
import React, { useCallback, useEffect, useState } from 'react'
import HtmlContentDisplay from './HtmlContentDisplay';
import TextEditor from './TextEditor';

const cssInputWrapper=`flex w-full flex-col h-fit gap-2 mx-auto`  
const ccsInputSection='bg-black/50 rounded-md p-2'  

function InputWrapper({ formData, handleInputChange, isEditing }) {
    return(
        <div className='flex flex-col w-full h-fit'>
            <div className={cssInputWrapper}>
                <label className='text-xl pt-5 capitalize' htmlFor="title">enter the title input here</label>
                <div className='flex flex-col w-full p-2 gap-2 rounded-md'>
                    <div className={ccsInputSection}>
                        <TextEditor
                            value={formData.title || ''}
                            onChange={(content) => handleInputChange('title', content)}
                            placeholder='Enter title with rich formatting...'
                            minHeight='80px'
                            className='w-full'
                        />
                    </div>
                    {/* <div className='bg-black p-4 min-h-12 rounded-md'>
                        <HtmlContentDisplay htmlString={formData.title} />
                    </div> */}
                </div>
                <div className={cssInputWrapper}>
                    <label className='text-xl pt-3 capitalize' htmlFor="body1">enter the body1 input here</label>
                    <div className='flex flex-col w-full px-2 gap-2 rounded-md'>
                        <div className={ccsInputSection}>
                            <TextEditor
                                value={formData.body1 || ''}
                                onChange={(content) => handleInputChange('body1', content)}
                                placeholder='Enter body content with rich formatting...'
                                minHeight='150px'
                                className='w-full'
                            />
                        </div>
                        {/* <div className='bg-black p-4 min-h-12 max-h-60 overflow-y-auto py-2 rounded-md'>
                            <HtmlContentDisplay htmlString={formData.body1} />
                        </div> */}
                    </div>
                </div>
                {/* create an image upload feature */}
                <div className={cssInputWrapper}>
                    <label className='text-xl pt-5 capitalize' htmlFor="body1">add thumbnail</label>
                    <div className='flex flex-col w-full bg-black/75 text-white py-4 pl-4 gap-2 rounded-md'>
                        <label htmlFor="Thumbnail">Thumbnail</label>
                        <input 
                            type="file" 
                            name="thumbnail" 
                            id="thumbnail" 
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default function InfoInput({ initialData = null, onSave, onCancel, isLoading = false }) {
  // Sample data for testing
      const sampleData = {
          title: '',
          body1: '',
          image: 'assets/art_piece_popup.png',
          additionalContent:[
              {
                  title: '',
                  body1: '',
                  image: 'assets/art_piece_popup.png',
              },
          ]
      };
      
      const [formData, setFormData] = useState({
          title: '',
          body1: '',
          image: 'assets/art_piece_popup.png',
      });
      
      const [additionalFormData, setAdditionalFormData] = useState([]);
      const [editingIndex, setEditingIndex] = useState(null); // Tracks the index of the item being edited
  
      const handleInputChange = useCallback((field, value) => {
          setFormData(prev => ({
            ...prev,
            [field]: value
          }));
      }, []);
      
      const handleAdditionalClick =() => {
          if(editingIndex !== null) {
              // Logic for editing an existing item
              const updatedContent = [...additionalFormData];
              updatedContent[editingIndex] = formData;
              setAdditionalFormData(updatedContent);
              setEditingIndex(null); // Exit edit mode
          } else {
              // Logic for adding a new item
              setAdditionalFormData([...additionalFormData, formData]);
          }
          setFormData({ title: '', body1: '', image: 'assets/art_piece_popup.png' }); // Clear the form after submission
      }
  
      const handleDeleteAdditional = (index) => {
          const updatedContent = additionalFormData.filter((_, i) => i !== index);
          setAdditionalFormData(updatedContent);
      };
  
      const handleEditAdditional = (index) => {
          setFormData(additionalFormData[index]); // Populate form with data from the selected item
          setEditingIndex(index); // Set the editing index
      };
      
      return (
      <div className='flex bg-gray-100 w-full h-full flex-col px-12 py-10 overflow-hidden'>
          <h1 className='text-3xl mb-5'>Page input test</h1>
          
          <div className='w-full h-full overflow-y-auto mb-10'>
              {/* SHOWING THE FORMAT-DATA OBJECT ENTRIES */}
              <div className='px-2 gap-5 h-fit bg-black w-full rounded-md'>
                  <div className='flex flex-col gap-2 px-2 text-white mr-2 shadow rounded-md'>
                      <div className='flex mt-2 w-full gap-5 flex-row-reverse items-start'>
                          <div className='flex mt-2 flex-col gap-2 flex-3/5'>
                              <div className='text-2xl capitalize'>
                                  {/* {formData?.title} */}
                                  <HtmlContentDisplay htmlString={formData.title} />
                              </div>
                              {/* <h1 className='text-2xl capitalize'>{formData?.title}</h1> */}
                              <div><HtmlContentDisplay htmlString={formData?.body1} /></div>
                          </div>
                          <img className='w-40 mt-2 aspect-auto' src={formData?.image} alt="" />
                      </div>
                  </div>
                  
                  {/* DISPLAYING ADDITIONAL CONTENT */}
                  <div className='flex h-fit flex-col w-full text-white'>
                      {additionalFormData?.length > 0 &&
                          <div className='text-center mt-5 mb-2 text-gray-400 capitalize'>Additional Content</div>
                      }
                      {additionalFormData?.map((item, index) => (
                          <div key={index} className='p-4 border-b border-gray-700 last:border-0 flex flex-col'>
                              <h1 className='text-xl capitalize'>{item.title}</h1>
                              <p>{item.body1}</p>
                              <div className='flex mt-2 gap-2'>
                                  <button onClick={() => handleEditAdditional(index)} className='text-white capitalize cursor-pointer h-8 px-3 bg-blue-600 rounded-md'>
                                      Edit
                                  </button>
                                  <button onClick={() => handleDeleteAdditional(index)} className='text-white capitalize cursor-pointer h-8 px-3 bg-red-600 rounded-md'>
                                      Delete
                                  </button>
                              </div>
                          </div>
                      ))}
                  </div>
              </div>
              
              {/* MAIN INPUT SECTION */}
              <InputWrapper
                  formData={formData}
                  handleInputChange={handleInputChange}
              />
              
              <div className='flex w-full mt-2 justify-between items-center'>
                  <div></div>
                  <div className='flex mr-4'>
                      <button onClick={handleAdditionalClick} className='text-white capitalize cursor-pointer h-10 w-fit text-xs px-4 bg-gray-700 rounded-md shadow'>
                          {editingIndex !== null ? 'Save Changes' : 'Add Additional Content'}
                      </button>
                  </div>
              </div>
          </div>
      </div>
    )
}
