'use client';

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import LocationAndContactsInput from '@/components/pages/LocationAndContactsInput';
import BookingDetailsText from '@/components/pages/BookingDetailsText';
import TestimonialsInput from '@/components/pages/TestimonialsInput';
import IslandAndInfoMarkerInput from '@/components/IslandAndInfoMarkerInput';

const PagesForm = React.memo(({ pages, onSave, onCancel, isLoading }) => {
  const [activeSection, setActiveSection] = useState('island');
  const [formData, setFormData] = useState({
    island: {
      title: '',
      image: '',
      body1: '',
      additionalContent: []
    },
    experiences: {
      title: '',
      image: '',
      body1: '',
      additionalContent: []
    },
    testimonials: {
      testimonials: []
    },
    locationAndcontacts: {
      title: '',
      body: '',
      details: ''
    },
    booking: {
      title: '',
      body1: '',
      body2: '',
      details: ''
    }
  });

  const [errors, setErrors] = useState({});

  // Initialize form data when pages prop changes
  useEffect(() => {
    if (pages) {
      setFormData({
        island: pages.island || {
          title: '',
          image: '',
          body1: '',
          additionalContent: []
        },
        experiences: pages.experiences || {
          title: '',
          image: '',
          body1: '',
          additionalContent: []
        },
        testimonials: pages.testimonials || {
          testimonials: []
        },
        locationAndcontacts: pages.locationAndcontacts || {
          title: '',
          body: '',
          details: ''
        },
        booking: pages.booking || {
          title: '',
          body1: '',
          body2: '',
          details: ''
        }
      });
    }
  }, [pages]);

  // Validation rules (optionally scoped to a single section)
  const validateForm = useCallback((section = null, dataOverride = null) => {
    const newErrors = {};

    // Helper to read field values from either override data (for section saves) or full formData
    const getField = (sec, field) => {
      if (section && sec !== section) return undefined;
      const base = section && sec === section ? (dataOverride || formData[sec]) : formData[sec];
      return base?.[field];
    };

    // Validate island section
    if (!section || section === 'island') {
      if (!getField('island', 'title')?.trim()) {
        newErrors['island.title'] = 'Island title is required';
      }
      if (!getField('island', 'body1')?.trim()) {
        newErrors['island.body1'] = 'Island body1 is required';
      }
    }

    // Validate experiences section
    if (!section || section === 'experiences') {
      if (!getField('experiences', 'title')?.trim()) {
        newErrors['experiences.title'] = 'Experiences title is required';
      }
      if (!getField('experiences', 'body1')?.trim()) {
        newErrors['experiences.body1'] = 'Experiences body1 is required';
      }
    }

    // Validate location and contacts section
    if (!section || section === 'locationAndcontacts') {
      const loc = section === 'locationAndcontacts' ? (dataOverride || formData.locationAndcontacts) : formData.locationAndcontacts;
      if (!loc?.title?.trim()) {
        newErrors['locationAndcontacts.title'] = 'Location title is required';
      }
      if (!loc?.body?.trim()) {
        newErrors['locationAndcontacts.body'] = 'Location body is required';
      }
      if (!loc?.details?.trim()) {
        newErrors['locationAndcontacts.details'] = 'Location details are required';
      }
    }

    // Validate booking section
    if (!section || section === 'booking') {
      if (!getField('booking', 'title')?.trim()) {
        newErrors['booking.title'] = 'Booking title is required';
      }
      if (!getField('booking', 'body1')?.trim()) {
        newErrors['booking.body1'] = 'Booking main description (Body 1) is required';
      }
      if (!getField('booking', 'details')?.trim()) {
        newErrors['booking.details'] = 'Booking details are required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle Quill editor content changes
  const handleQuillChange = useCallback((content, section, fieldName) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [fieldName]: content
      }
    }));

    // Clear error when user starts typing
    if (errors[`${section}.${fieldName}`]) {
      setErrors(prev => ({
        ...prev,
        [`${section}.${fieldName}`]: ''
      }));
    }
  }, [errors]);

  // Handle form submission
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSave(formData);
    } catch (error) {
      console.error('Save error:', error);
    }
  }, [formData, validateForm, onSave]);

  // Handle section save
  const handleSectionSave = useCallback(async (section, sectionData = null) => {
    try {
      if (section) {
        const dataToSave = sectionData || formData[section];
        console.log('[UI] PagesForm.handleSectionSave', section, dataToSave);

        // Validate only the provided section to avoid unrelated validation failures
        const isValid = validateForm(section, dataToSave);
        if (!isValid) return;
        await onSave({ section, data: dataToSave });
      } else {
        // Saving entire form validates all sections
        if (!validateForm()) return;
        await onSave(formData);
      }
    } catch (error) {
      console.error('Save error:', error);
      throw error;
    }
  }, [formData, validateForm, onSave]);

  // Memoized section options
  const sectionOptions = useMemo(() => [
    { value: 'island', label: 'The Island' },
    { value: 'experiences', label: 'Experiences' },
    { value: 'testimonials', label: 'Testimonials' },
    { value: 'locationAndcontacts', label: 'Location & Contacts' },
    { value: 'booking', label: 'Booking' }
  ], []);

  // console.log(pages?.island)

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-trasandina-black text-gray-900 uppercase tracking-wide">
          Pages Management
        </h2>
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            form="pages-form"
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save All Pages'}
          </button>
        </div>
      </div>

      {/* Section Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {sectionOptions.map((section) => (
            <button
              key={section.value}
              type="button"
              onClick={() => setActiveSection(section.value)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeSection === section.value
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {section.label}
            </button>
          ))}
        </nav>
      </div>

      <form id="pages-form" onSubmit={handleSubmit} className="space-y-6">
        {/* Island Section */}
        <div>
          {activeSection === 'island' && (
            <IslandAndInfoMarkerInput
              pages={pages}
              onSave={onSave}
              isLoading={isLoading}
            />
          )}
        </div>

        {/* Experiences Section */}
        {activeSection === 'experiences' && (
          <IslandAndInfoMarkerInput
            pages={pages}
            onSave={onSave}
            isLoading={isLoading}
            section="experiences"
            heading="Experiences Page Content"
          />
        )}

        {/* Testimonials Section */}
        {activeSection === 'testimonials' && (
          <TestimonialsInput
            formData={formData.testimonials}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={() => handleSectionSave('testimonials')}
            isLoading={isLoading}
          />
        )}

        {/* Location and Contacts Section */}
        {activeSection === 'locationAndcontacts' && (
          <LocationAndContactsInput
            formData={formData.locationAndcontacts}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={(_, sectionData) => handleSectionSave('locationAndcontacts', sectionData)}
            isLoading={isLoading}
          />
        )}

        {/* Booking Section */}
        {activeSection === 'booking' && (
          <BookingDetailsText
            formData={formData.booking}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={(section, sectionData) => handleSectionSave(section, sectionData)}
            isLoading={isLoading}
          />
        )}
      </form>
    </div>
  );
});

// Set display names
PagesForm.displayName = 'PagesForm';

export default PagesForm;
