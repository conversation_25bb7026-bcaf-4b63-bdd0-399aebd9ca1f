'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import TextEditor from '@/components/TextEditor';
import ActionButtonGroup from '@/components/pages/ActionButtonGroup';

/**
 * BookingDetailsText Component
 *
 * Integrated with TextEditor component from test-text-editor page
 *
 * API Endpoints Used:
 * - GET /api/pages - Fetch all page data including booking section
 * - POST /api/pages - Update specific page section (booking)
 * - PATCH /api/pages - Update multiple sections at once
 *
 * Features:
 * - Rich text editing with TextEditor component
 * - Sample content loading
 * - Direct API submission
 * - Enhanced error handling and user feedback
 * - Line height customization
 * - Text selection controls
 */

const BookingDetailsText = ({
  formData,
  errors,
  onQuillChange,
  onSectionSave,
  isLoading
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [dataError, setDataError] = useState(null);
  const [localFormData, setLocalFormData] = useState({
    title: '',
    body1: '',
    body2: '',
    details: ''
  });

  // Centralized line height state management
  // - global: Default line height for the entire editor
  // - current: Currently active line height (global or selection-specific)
  // - hasSelection: Whether text is currently selected
  const [lineHeightState, setLineHeightState] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('bookingDetailsLineHeight');
      return {
        global: saved ? parseFloat(saved) : 1.6,
        current: saved ? parseFloat(saved) : 1.6,
        hasSelection: false
      };
    }
    return {
      global: 1.6,
      current: 1.6,
      hasSelection: false
    };
  });

  const [selectedText, setSelectedText] = useState('');
  const [showSelectionControls, setShowSelectionControls] = useState(false);
  const titleEditorRef = useRef(null);
  const body1EditorRef = useRef(null);
  const body2EditorRef = useRef(null);
  const detailsEditorRef = useRef(null);

  // Available line height options for validation
  const availableLineHeights = [0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2, 2.4];

  // Find the nearest available line height option
  const findNearestAvailableLineHeight = useCallback((value) => {
    return availableLineHeights.reduce((prev, curr) =>
      Math.abs(curr - value) < Math.abs(prev - value) ? curr : prev
    );
  }, [availableLineHeights]);

  // Find the most common line height from an array
  const findMostCommonLineHeight = useCallback((lineHeights) => {
    if (lineHeights.length === 1) return lineHeights[0];

    const frequency = {};
    lineHeights.forEach(lh => {
      frequency[lh] = (frequency[lh] || 0) + 1;
    });

    // Return the most frequent value, or the first one if tied
    const mostCommon = Object.keys(frequency).reduce((a, b) =>
      frequency[a] > frequency[b] ? a : b
    );

    return parseFloat(mostCommon);
  }, []);

  // Validate and normalize detected line height
  const validateLineHeight = useCallback((detectedValue) => {
    // Ensure the value is a number
    const numValue = parseFloat(detectedValue);
    if (isNaN(numValue)) {
      return lineHeightState.global;
    }

    // Check if it's exactly one of our available options
    if (availableLineHeights.includes(numValue)) {
      return numValue;
    }

    // Find the closest available option
    const closest = findNearestAvailableLineHeight(numValue);
    console.log(`📐 Normalized line height ${numValue} to closest available option: ${closest}`);
    return closest;
  }, [lineHeightState.global, availableLineHeights, findNearestAvailableLineHeight]);

  // Extract line height from a single element
  const extractLineHeightFromElement = useCallback((element, contentEditable) => {
    if (!element || element === contentEditable) return null;

    // Check inline style first (most reliable for our custom line heights)
    if (element.style && element.style.lineHeight) {
      const inlineValue = parseFloat(element.style.lineHeight);
      if (!isNaN(inlineValue) && inlineValue >= 0.1 && inlineValue <= 10) {
        return inlineValue;
      }
    }

    // Check computed style
    try {
      const computedStyle = window.getComputedStyle(element);
      const computedLineHeight = computedStyle.lineHeight;

      if (computedLineHeight && computedLineHeight !== 'normal' && computedLineHeight !== 'inherit') {
        if (computedLineHeight.endsWith('px')) {
          // Convert pixel values to relative values
          const fontSize = parseFloat(computedStyle.fontSize);
          if (fontSize > 0) {
            const pixelLineHeight = parseFloat(computedLineHeight);
            const relativeLineHeight = pixelLineHeight / fontSize;
            if (!isNaN(relativeLineHeight) && relativeLineHeight >= 0.1 && relativeLineHeight <= 10) {
              // Round to nearest available option
              return findNearestAvailableLineHeight(relativeLineHeight);
            }
          }
        } else {
          // Handle unitless values
          const numericValue = parseFloat(computedLineHeight);
          if (!isNaN(numericValue) && numericValue >= 0.1 && numericValue <= 10) {
            return findNearestAvailableLineHeight(numericValue);
          }
        }
      }
    } catch (error) {
      console.warn('Error getting computed style for element:', error);
    }

    return null;
  }, [findNearestAvailableLineHeight]);

  // Robust line height detection for selected text
  const detectSelectionLineHeight = useCallback((selection) => {
    if (!selection || selection.rangeCount === 0) {
      return lineHeightState.global;
    }

    const range = selection.getRangeAt(0);
    const contentEditable = detailsEditorRef.current?.querySelector('[contenteditable="true"]');

    // Collect all line height values from the selection
    const lineHeights = [];

    // Method 1: Check if selection spans multiple elements
    const selectedElements = [];

    // Get all elements that intersect with the selection
    if (range.commonAncestorContainer) {
      const walker = document.createTreeWalker(
        range.commonAncestorContainer,
        NodeFilter.SHOW_ELEMENT,
        {
          acceptNode: (node) => {
            // Check if this node intersects with our selection
            const nodeRange = document.createRange();
            try {
              nodeRange.selectNodeContents(node);
              return (range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0 &&
                      range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0)
                      ? NodeFilter.FILTER_ACCEPT
                      : NodeFilter.FILTER_SKIP;
            } catch (e) {
              return NodeFilter.FILTER_SKIP;
            }
          }
        }
      );

      let node;
      while (node = walker.nextNode()) {
        if (node !== contentEditable && !contentEditable?.contains(node)) continue;
        selectedElements.push(node);
      }
    }

    // Method 2: Also check the direct parent elements of the range
    let startElement = range.startContainer;
    let endElement = range.endContainer;

    if (startElement.nodeType === Node.TEXT_NODE) {
      startElement = startElement.parentElement;
    }
    if (endElement.nodeType === Node.TEXT_NODE) {
      endElement = endElement.parentElement;
    }

    const elementsToCheck = [...selectedElements, startElement, endElement];

    // Remove duplicates and null values
    const uniqueElements = [...new Set(elementsToCheck.filter(el => el && el !== contentEditable))];

    console.log('🔍 Checking line height on elements:', uniqueElements.length, 'elements');

    // Extract line heights from all relevant elements
    for (const element of uniqueElements) {
      const lineHeight = extractLineHeightFromElement(element, contentEditable);
      if (lineHeight !== null) {
        lineHeights.push(lineHeight);
        console.log('📏 Found line height:', lineHeight, 'on element:', element.tagName, element.className);
      }
    }

    // Determine the best line height value
    if (lineHeights.length === 0) {
      console.log('⚠️ No line heights detected, using global default:', lineHeightState.global);
      return lineHeightState.global;
    }

    // If we have multiple values, use the most common one, or the first one
    const mostCommon = findMostCommonLineHeight(lineHeights);
    const validated = validateLineHeight(mostCommon);
    console.log('✅ Selected line height:', validated, 'from detected values:', lineHeights, 'most common:', mostCommon);

    return validated;
  }, [lineHeightState.global, extractLineHeightFromElement, findMostCommonLineHeight, validateLineHeight]);

  // Update local form data when formData changes
  useEffect(() => {
    if (formData) {
      setLocalFormData({
        title: formData.title || '',
        body1: formData.body1 || '',
        body2: formData.body2 || '',
        details: formData.details || ''
      });
    }
  }, [formData]);

  // Attach selection listeners to TextEditor's contentEditable element
  useEffect(() => {
    if (isEditing && detailsEditorRef.current) {
      // Find the contentEditable element within the TextEditor (details field)
      const findContentEditable = () => {
        const contentEditable = detailsEditorRef.current?.querySelector('[contenteditable="true"]');
        return contentEditable;
      };

      // Create the selection handler inside the effect to avoid dependency issues
      const handleSelection = () => {
        try {
          const selection = window.getSelection();
          if (selection && selection.toString().trim()) {
            setSelectedText(selection.toString());
            setShowSelectionControls(true);

            // Detect current line height of selected text with improved accuracy
            if (selection.rangeCount > 0) {
              const detectedLineHeight = detectSelectionLineHeight(selection);

              console.log('🎯 WYSIWYG Line Height Detection:', {
                selectedText: selection.toString().substring(0, 50),
                detectedValue: detectedLineHeight,
                previousValue: lineHeightState.current,
                availableOptions: availableLineHeights
              });

              setLineHeightState(prev => ({
                ...prev,
                current: detectedLineHeight,
                hasSelection: true
              }));
            }
          } else {
            setSelectedText('');
            setShowSelectionControls(false);
            // Reset to global line height when no selection
            setLineHeightState(prev => ({
              ...prev,
              current: prev.global,
              hasSelection: false
            }));
          }
        } catch (error) {
          console.error('Error in selection handler:', error);
          setSelectedText('');
          setShowSelectionControls(false);
        }
      };

      // Use a timeout to ensure TextEditor has rendered
      const timeoutId = setTimeout(() => {
        const contentEditable = findContentEditable();
        if (contentEditable) {
          contentEditable.addEventListener('mouseup', handleSelection);
          contentEditable.addEventListener('keyup', handleSelection);
          contentEditable.addEventListener('focus', handleSelection);
        }
      }, 100);

      return () => {
        clearTimeout(timeoutId);
        const contentEditable = findContentEditable();
        if (contentEditable) {
          contentEditable.removeEventListener('mouseup', handleSelection);
          contentEditable.removeEventListener('keyup', handleSelection);
          contentEditable.removeEventListener('focus', handleSelection);
        }
      };
    }
  }, [isEditing, lineHeightState.global, detectSelectionLineHeight]);

  // Clean up selection state when exiting edit mode
  useEffect(() => {
    if (!isEditing) {
      setSelectedText('');
      setShowSelectionControls(false);
      setLineHeightState(prev => ({
        ...prev,
        current: prev.global,
        hasSelection: false
      }));
    }
  }, [isEditing]);

  // Handle local form changes when in edit mode
  const handleLocalChange = useCallback((content, field) => {
    setLocalFormData(prev => ({
      ...prev,
      [field]: content
    }));
  }, []);

  // Handle loading sample content (similar to test-text-editor)
  const handleLoadSampleContent = useCallback(() => {
    const sampleTitle = `<p><strong>Booking</strong>: Plan Your Experience</p>`;
    const sampleBody1 = `<p>Welcome to the booking section. Please review the options, pick a suitable date, and provide your contact information. Our system supports flexible scheduling and instant email confirmations.</p>`;
    const sampleBody2 = `<p>Need help? Our team is available 24/7 to assist you. For special requests, email <a href="mailto:<EMAIL>" style="color: blue; text-decoration: underline;"><EMAIL></a>.</p>`;
    const sampleDetails = `<ul><li>Arrive 15 minutes before your scheduled time</li><li>Bring a valid ID and your confirmation email</li><li>Cancellation policy: 24 hours prior to booking</li></ul>`;
    handleLocalChange(sampleTitle, 'title');
    handleLocalChange(sampleBody1, 'body1');
    handleLocalChange(sampleBody2, 'body2');
    handleLocalChange(sampleDetails, 'details');
  }, [handleLocalChange]);

  // Handle clearing content
  const handleClearContent = useCallback(() => {
    handleLocalChange('', 'title');
    handleLocalChange('', 'body1');
    handleLocalChange('', 'body2');
    handleLocalChange('', 'details');
  }, [handleLocalChange]);

  // Helper function to show success messages
  const showSuccessMessage = useCallback((message) => {
    const successMessage = document.createElement('div');
    successMessage.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50 flex items-center space-x-2';
    successMessage.innerHTML = `
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      <span>${message}</span>
    `;
    document.body.appendChild(successMessage);
    setTimeout(() => {
      if (document.body.contains(successMessage)) {
        document.body.removeChild(successMessage);
      }
    }, 3000);
  }, []);

  // Helper function to show error messages
  const showErrorMessage = useCallback((message) => {
    const errorMessage = document.createElement('div');
    errorMessage.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-md shadow-lg z-50 flex items-center space-x-2';
    errorMessage.innerHTML = `
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
      <span>${message}</span>
    `;
    document.body.appendChild(errorMessage);
    setTimeout(() => {
      if (document.body.contains(errorMessage)) {
        document.body.removeChild(errorMessage);
      }
    }, 5000);
  }, []);

  // Enhanced validation function
  const validateBookingDetails = useCallback((data) => {
    const errors = [];

    if (!data.title || !data.title.trim()) {
      errors.push('Title is required');
    }
    if (!data.body1 || !data.body1.trim()) {
      errors.push('Main description (Body 1) is required');
    }
    if (!data.details || !data.details.trim()) {
      errors.push('Booking details are required');
    }

    if (data.details && data.details.replace(/<[^>]*>/g, '').trim().length < 10) {
      errors.push('Booking details must be at least 10 characters long');
    }

    return errors;
  }, []);

  // Direct API submission function for booking details
  const submitBookingDetailsDirectly = useCallback(async (bookingData) => {
    try {
      console.log('🚀 Submitting booking details directly to API:', bookingData);

      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'booking',
          data: bookingData
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        console.log('✅ Booking details submitted successfully:', result);
        return result;
      } else {
        throw new Error(result.message || 'Failed to submit booking details');
      }
    } catch (error) {
      console.error('❌ Error submitting booking details:', error);
      throw error;
    }
  }, []);

  // Apply line height to selected text
  const applyLineHeightToSelection = useCallback((newLineHeight) => {
    const selection = window.getSelection();

    if (selection && selection.rangeCount > 0 && !selection.isCollapsed) {
      const range = selection.getRangeAt(0);

      try {
        // Create a span element with the line height style
        const span = document.createElement('span');
        span.style.lineHeight = newLineHeight.toString();
        span.style.display = 'inline-block'; // Ensure line-height applies properly

        // Try to surround the contents with the span
        try {
          range.surroundContents(span);
        } catch (error) {
          // If surroundContents fails, use extractContents approach
          const contents = range.extractContents();
          span.appendChild(contents);
          range.insertNode(span);
        }

        // Update the content by getting it from the TextEditor's contentEditable element (details)
        if (detailsEditorRef.current) {
          const contentEditable = detailsEditorRef.current.querySelector('[contenteditable="true"]');
          if (contentEditable) {
            const newContent = contentEditable.innerHTML;
            handleLocalChange(newContent, 'details');
          }
        }

        // Clear selection and update state
        selection.removeAllRanges();
        setShowSelectionControls(false);
        setSelectedText('');
        setLineHeightState(prev => ({
          ...prev,
          hasSelection: false,
          current: prev.global
        }));

      } catch (error) {
        console.error('Failed to apply line height to selection:', error);
      }
    }
  }, [handleLocalChange]);

  // Centralized line height change handler
  const handleLineHeightChange = useCallback((value, applyToSelection = false) => {
    const newLineHeight = parseFloat(value);
    console.log('🔧 Line height change:', { value, newLineHeight, applyToSelection, hasSelection: lineHeightState.hasSelection });

    if (applyToSelection && lineHeightState.hasSelection) {
      console.log('📝 Applying to selection:', newLineHeight);
      // Apply to selected text using existing function
      applyLineHeightToSelection(newLineHeight);

      // Update current line height for selection
      setLineHeightState(prev => ({
        ...prev,
        current: newLineHeight
      }));
    } else {
      console.log('🌐 Updating global line height:', newLineHeight);
      // Update global line height
      setLineHeightState(prev => ({
        ...prev,
        global: newLineHeight,
        current: prev.hasSelection ? prev.current : newLineHeight
      }));

      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('bookingDetailsLineHeight', newLineHeight.toString());
      }
    }
  }, [lineHeightState.hasSelection, applyLineHeightToSelection]);

  // Clear all line height formatting
  const clearAllLineHeightFormatting = useCallback(() => {
    if (detailsEditorRef.current) {
      const contentEditable = detailsEditorRef.current.querySelector('[contenteditable="true"]');
      if (contentEditable) {
        const content = contentEditable.innerHTML;
        // Remove all line-height styles from spans
        const cleanedContent = content.replace(/style="[^"]*line-height:[^;"]*;?[^"]*"/g, (match) => {
          // Keep other styles but remove line-height
          const styleContent = match.match(/style="([^"]*)"/)[1];
          const cleanedStyle = styleContent
            .split(';')
            .filter(style => !style.trim().startsWith('line-height') && !style.trim().startsWith('display'))
            .join(';');
          return cleanedStyle ? `style="${cleanedStyle}"` : '';
        });

        handleLocalChange(cleanedContent, 'details');
      }
    }
  }, [handleLocalChange]);

  // Fetch fresh data when entering edit mode with enhanced error handling
  const fetchLatestData = useCallback(async () => {
    setIsLoadingData(true);
    setDataError(null);

    try {
      const response = await fetch('/api/pages', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        const bookingData = data.data?.booking || {};
        const freshFormData = {
          title: bookingData.title || '',
          body1: bookingData.body1 || '',
          body2: bookingData.body2 || '',
          details: bookingData.details || ''
        };
        setLocalFormData(freshFormData);
        console.log('✅ Successfully fetched latest booking data');
      } else {
        throw new Error(data.message || 'Failed to fetch latest data');
      }
    } catch (error) {
      console.error('❌ Error fetching latest booking data:', error);
      setDataError(`Failed to load latest data: ${error.message}. Using current values.`);
      // Fallback to current form data
      const currentFormData = {
        title: formData?.title || '',
        body1: formData?.body1 || '',
        body2: formData?.body2 || '',
        details: formData?.details || ''
      };
      setLocalFormData(currentFormData);
    } finally {
      setIsLoadingData(false);
    }
  }, [formData]);

  // Handle edit mode toggle
  const handleEdit = useCallback(async () => {
    setIsEditing(true);
    await fetchLatestData();
  }, [fetchLatestData]);



  // Handle save changes with enhanced error handling and API integration
  const handleSave = useCallback(async () => {
    try {
      // Enhanced validation with fallback
      const validationErrors = validateBookingDetails ? validateBookingDetails(localFormData) : [];
      if (validationErrors.length > 0) {
        const errorMessage = validationErrors.join(', ');
        setDataError(errorMessage);
        showErrorMessage && showErrorMessage(errorMessage);
        return;
      }

      // Clear any previous errors
      setDataError(null);

      // Update the parent form data first (synchronously)

      console.log('[UI] BookingDetailsText.handleSave payload:', localFormData);

      Object.keys(localFormData).forEach(field => {
        onQuillChange(localFormData[field], 'booking', field);
      });

      // Wait a brief moment for state updates to propagate
      await new Promise(resolve => setTimeout(resolve, 100));

      // Save to API with enhanced error handling
      try {
        await onSectionSave('booking', localFormData);
        setIsEditing(false);

        // Show success feedback
        showSuccessMessage('Booking details saved successfully!');

      } catch (apiError) {
        console.error('API Error saving booking:', apiError);
        const errorMsg = `Failed to save changes: ${apiError.message || 'Unknown error occurred'}`;
        setDataError(errorMsg);
        showErrorMessage(errorMsg);
        // Don't exit edit mode if save failed
      }
    } catch (error) {
      console.error('Error saving booking:', error);
      const errorMsg = `Failed to save changes: ${error.message || 'Please try again.'}`;
      setDataError(errorMsg);
      showErrorMessage(errorMsg);
      // Don't exit edit mode if save failed
    }
  }, [localFormData, onQuillChange, onSectionSave, validateBookingDetails, showSuccessMessage, showErrorMessage]);

  // Handle direct save to API (bypassing parent form)
  const handleDirectSave = useCallback(async () => {
    try {
      // Enhanced validation with fallback
      const validationErrors = validateBookingDetails ? validateBookingDetails(localFormData) : [];
      if (validationErrors.length > 0) {
        const errorMessage = validationErrors.join(', ');
        setDataError(errorMessage);
        showErrorMessage && showErrorMessage(errorMessage);
        return;
      }


      console.log('[UI] BookingDetailsText.handleDirectSave payload:', localFormData);

      // Clear any previous errors
      setDataError(null);

      // Save directly to API
      await submitBookingDetailsDirectly(localFormData);
      setIsEditing(false);

      // Show success feedback
      showSuccessMessage('Booking details saved directly to database!');

    } catch (error) {
      console.error('Error saving booking directly:', error);
      const errorMsg = `Failed to save changes: ${error.message || 'Please try again.'}`;
      setDataError(errorMsg);
      showErrorMessage(errorMsg);
      // Don't exit edit mode if save failed
    }
  }, [localFormData, submitBookingDetailsDirectly, validateBookingDetails, showSuccessMessage, showErrorMessage]);

  // Handle cancel edit
  const handleCancel = useCallback(() => {
    // Reset to current form data when canceling
    setLocalFormData({
      title: formData?.title || '',
      body1: formData?.body1 || '',
      body2: formData?.body2 || '',
      details: formData?.details || ''
    });
    setIsEditing(false);
  }, [formData]);

  // Handle delete
  const handleDelete = useCallback(async () => {
    try {
      // Clear all fields
      const emptyData = {
        title: '',
        body1: '',
        body2: '',
        details: ''
      };

      Object.keys(emptyData).forEach(field => {
        onQuillChange(emptyData[field], 'booking', field);
      });

      await onSectionSave('booking', emptyData);
      setShowDeleteConfirm(false);
      setIsEditing(false);
    } catch (error) {
      console.error('Error deleting booking:', error);
    }
  }, [onQuillChange, onSectionSave]);

  const currentData = isEditing ? localFormData : formData;
  const hasContent = formData.title || formData.body1 || formData.body2 || formData.details;

  console.log(currentData)

  return (
    <>
      {/* CSS for preserving line heights */}
      <style jsx>{`
        .preserve-line-heights span[style*="line-height"] {
          display: inline-block;
        }
        .selection-highlight {
          background-color: rgba(59, 130, 246, 0.1);
          border-radius: 2px;
          padding: 1px 2px;
        }
      `}</style>

    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Booking Details</h3>

        <ActionButtonGroup
          mode={isEditing ? 'edit' : 'view'}
          isLoading={isLoadingData || isLoading}
          hasContent={hasContent}
          onEdit={handleEdit}
          onSave={handleSave}
          onCancel={handleCancel}
          onDelete={() => setShowDeleteConfirm(true)}
          onDirectSave={handleDirectSave}
          onLoadSample={handleLoadSampleContent}
          showDirectSave={true}
          showLoadSample={true}
          showDelete={true}
          responsive={true}
          customButtons={[
            {
              mode: 'view',
              variant: 'success',
              onClick: onSectionSave,
              disabled: isLoading,
              loading: isLoading,
              loadingText: 'Saving...',
              label: 'Save Section'
            }
          ]}
        />
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <h3 className="text-lg font-medium text-gray-900">Confirm Delete</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete all Booking content? This action cannot be undone.
                </p>
              </div>
              <div className="flex justify-center space-x-3 mt-4">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Error Display */}
      {dataError && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-800">{dataError}</p>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoadingData && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
            <p className="text-sm text-blue-800">Loading latest data...</p>
          </div>
        </div>
      )}

      {/* Content Display/Edit Form */}
      {!hasContent && !isEditing ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <p className="text-gray-500 mb-4">No booking content available</p>
          <button
            onClick={handleEdit}
            disabled={isLoadingData}
            className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoadingData ? 'Loading...' : 'Create Content'}
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Details */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Booking Details *
              </label>
              {isEditing && (
                <div className="flex items-center space-x-2">
                  <label className="text-xs text-gray-500">
                    {lineHeightState.hasSelection ? 'Selection' : 'Default'} Line Height:
                  </label>
                  <select
                    value={lineHeightState.current}
                    onChange={(e) => handleLineHeightChange(e.target.value, lineHeightState.hasSelection)}
                    className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    <option value="0.4">0.4</option>
                    <option value="0.6">0.6</option>
                    <option value="0.8">0.8</option>
                    <option value="1.0">1.0</option>
                    <option value="1.2">1.2</option>
                    <option value="1.4">1.4</option>
                    <option value="1.6">1.6</option>
                    <option value="1.8">1.8</option>
                    <option value="2.0">2.0</option>
                    <option value="2.2">2.2</option>
                    <option value="2.4">2.4</option>
                  </select>
                  {currentData.details && currentData.details.includes('line-height') && (
                    <button
                      type="button"
                      onClick={clearAllLineHeightFormatting}
                      className="text-xs px-2 py-1 text-red-600 bg-red-50 border border-red-200 rounded hover:bg-red-100 focus:outline-none focus:ring-1 focus:ring-red-500"
                      title="Clear all custom line heights"
                    >
                      Clear All
                    </button>
                  )}
                </div>
              )}
            </div>
            {isEditing ? (
              <div className="space-y-3">
                {/* Content Control Buttons */}
                <div className="flex gap-2 mb-3">
                  <button
                    type="button"
                    onClick={handleLoadSampleContent}
                    className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    Load Sample Content
                  </button>
                  <button
                    type="button"
                    onClick={handleClearContent}
                    className="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-1 focus:ring-gray-500"
                  >
                    Clear Content
                  </button>
                </div>

                {/* Selection indicator */}
                {showSelectionControls && selectedText && (
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-blue-800">
                        Selected: "{selectedText.length > 30 ? selectedText.substring(0, 30) + '...' : selectedText}"
                      </span>
                      <span className="text-xs text-blue-600">
                        (Line height: {lineHeightState.current})
                      </span>
                      <span className="text-xs text-gray-500">
                        • Use the line height control above to modify
                      </span>
                    </div>
                  </div>
                )}

                {/* Help text when no selection */}
                {!showSelectionControls && (
                  <div className="bg-gray-50 border border-gray-200 rounded-md p-2">
                    <p className="text-xs text-gray-600">
                      💡 Select text to apply custom line height to specific portions, or use the global setting above for new text. Use the buttons above to load sample content or clear the editor.
                    </p>
                  </div>
                )}
                <p className='text-lg capitalize text-gray-500'>add title details here</p>
                <div
                  className={`${errors['booking.title'] ? 'border-red-500' : ''}`}
                  ref={titleEditorRef}
                >
                  <TextEditor
                    key={`title-edit-${isEditing}`}
                    value={currentData.title}
                    onChange={(content) => handleLocalChange(content, 'title')}
                    placeholder="Enter booking title"
                    style={{ minHeight: '20px', lineHeight: lineHeightState.global }}
                    className={`border rounded-md ${errors['booking.title'] ? 'border-red-500' : 'border-gray-300'}`}
                  />
                </div>
                <p className='text-lg capitalize text-gray-500'>add body 1 details here</p>
                <div
                  className={`${errors['booking.body1'] ? 'border-red-500' : ''}`}
                  ref={body1EditorRef}
                >
                  <TextEditor
                    key={`body1-edit-${isEditing}`}
                    value={currentData.body1}
                    onChange={(content) => handleLocalChange(content, 'body1')}
                    placeholder="Enter main booking description"
                    style={{ minHeight: '40px', lineHeight: lineHeightState.global }}
                    className={`border rounded-md ${errors['booking.body1'] ? 'border-red-500' : 'border-gray-300'}`}
                  />
                </div>
                <p className='text-lg capitalize text-gray-500'>add body 2 details here</p>
                <div
                  className={`${errors['booking.body2'] ? 'border-red-500' : ''}`}
                  ref={body2EditorRef}
                >
                  <TextEditor
                    key={`body2-edit-${isEditing}`}
                    value={currentData.body2}
                    onChange={(content) => handleLocalChange(content, 'body2')}
                    placeholder="Enter additional booking information"
                    style={{ minHeight: '40px', lineHeight: lineHeightState.global }}
                    className={`border rounded-md ${errors['booking.body2'] ? 'border-red-500' : 'border-gray-300'}`}
                  />
                </div>
                <p className='text-lg capitalize text-gray-500'>add details input here</p>
                <div
                  className={`${errors['booking.details'] ? 'border-red-500' : ''}`}
                  ref={detailsEditorRef}
                >
                  <TextEditor
                    key={`details-edit-${isEditing}`}
                    value={currentData.details}
                    onChange={(content) => handleLocalChange(content, 'details')}
                    placeholder="Enter booking details/instructions"
                    style={{ minHeight: '40px', lineHeight: lineHeightState.global }}
                    className={`border rounded-md ${errors['booking.details'] ? 'border-red-500' : 'border-gray-300'}`}
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                  <div className="p-4 bg-gray-50 rounded-md border" style={{ lineHeight: lineHeightState.global }}>
                    <div
                      dangerouslySetInnerHTML={{ __html: currentData.title || 'No title set' }}
                      className="preserve-line-heights"
                    />
                  </div>
                </div>

                {/* Body 1 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Body 1</label>
                  <div className="p-4 bg-gray-50 rounded-md border" style={{ lineHeight: lineHeightState.global }}>
                    <div
                      dangerouslySetInnerHTML={{ __html: currentData.body1 || 'No body 1 content set' }}
                      className="preserve-line-heights"
                    />
                  </div>
                </div>

                {/* Body 2 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Body 2</label>
                  <div className="p-4 bg-gray-50 rounded-md border" style={{ lineHeight: lineHeightState.global }}>
                    <div
                      dangerouslySetInnerHTML={{ __html: currentData.body2 || 'No body 2 content set' }}
                      className="preserve-line-heights"
                    />
                  </div>
                </div>

                {/* Details */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Details</label>
                  <div className="p-4 bg-gray-50 rounded-md border" style={{ lineHeight: lineHeightState.global }}>
                    <div
                      dangerouslySetInnerHTML={{ __html: currentData.details || 'No booking details set' }}
                      className="preserve-line-heights"
                    />
                  </div>
                </div>
              </div>
            )}
            {errors['booking.title'] && (
              <p className="mt-1 text-sm text-red-600">{errors['booking.title']}</p>
            )}
            {errors['booking.body1'] && (
              <p className="mt-1 text-sm text-red-600">{errors['booking.body1']}</p>
            )}
            {errors['booking.body2'] && (
              <p className="mt-1 text-sm text-red-600">{errors['booking.body2']}</p>
            )}
            {errors['booking.details'] && (
              <p className="mt-1 text-sm text-red-600">{errors['booking.details']}</p>
            )}
          </div>
        </div>
      )}
    </div>
    </>
  );
};

export default BookingDetailsText;
