'use client';

import React from 'react';

/**
 * Standardized Action Button Group Component for Page Management
 * 
 * Provides consistent styling, positioning, and behavior for all page management components
 * 
 * Features:
 * - Consistent button styling and colors
 * - Responsive design with proper wrapping
 * - Loading states and disabled styling
 * - Proper button grouping and spacing
 * - Accessibility features (focus rings, ARIA labels)
 */

// Base button styles for consistency
const baseButtonStyles = "px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";

// Button variant styles
const buttonVariants = {
  // Primary actions (Save, Edit)
  primary: "text-white bg-blue-600 border border-transparent hover:bg-blue-700 focus:ring-blue-500",
  
  // Secondary actions (Cancel)
  secondary: "text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:ring-gray-500",
  
  // Destructive actions (Delete)
  destructive: "text-white bg-red-600 border border-transparent hover:bg-red-700 focus:ring-red-500",
  
  // Success actions (Save Direct, Load Sample)
  success: "text-white bg-green-600 border border-transparent hover:bg-green-700 focus:ring-green-500",
  
  // Special actions (Save Direct alternative)
  purple: "text-white bg-purple-600 border border-transparent hover:bg-purple-700 focus:ring-purple-500",
  
  // Outlined primary (Edit in view mode)
  outlinedPrimary: "text-blue-600 bg-blue-50 border border-blue-200 hover:bg-blue-100 focus:ring-blue-500",
  
  // Outlined destructive (Delete in view mode)
  outlinedDestructive: "text-red-600 bg-red-50 border border-red-200 hover:bg-red-100 focus:ring-red-500"
};

// Individual Button Component
const ActionButton = ({ 
  variant = 'primary', 
  size = 'default',
  children, 
  disabled = false, 
  loading = false, 
  loadingText,
  onClick,
  type = 'button',
  className = '',
  ...props 
}) => {
  const sizeStyles = {
    small: 'px-3 py-1.5 text-xs',
    default: 'px-4 py-2 text-sm',
    large: 'px-6 py-3 text-base'
  };

  const buttonClasses = `
    ${baseButtonStyles}
    ${buttonVariants[variant] || buttonVariants.primary}
    ${sizeStyles[size]}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={buttonClasses}
      {...props}
    >
      {loading ? (loadingText || 'Loading...') : children}
    </button>
  );
};

// Main ActionButtonGroup Component
const ActionButtonGroup = ({ 
  mode = 'view', // 'view' or 'edit'
  isLoading = false,
  hasContent = false,
  onEdit,
  onSave,
  onCancel,
  onDelete,
  onDirectSave,
  onLoadSample,
  showDirectSave = true,
  showLoadSample = true,
  showDelete = true,
  customButtons = [],
  className = '',
  responsive = true
}) => {
  const containerClasses = `
    flex items-center gap-2
    ${responsive ? 'flex-wrap sm:flex-nowrap' : ''}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  // View mode buttons
  if (mode === 'view') {
    return (
      <div className={containerClasses}>
        <ActionButton
          variant="outlinedPrimary"
          onClick={onEdit}
          disabled={isLoading}
          loading={isLoading}
          loadingText="Loading..."
        >
          {isLoading ? 'Loading...' : 'Edit'}
        </ActionButton>
        
        {hasContent && showDelete && (
          <ActionButton
            variant="outlinedDestructive"
            onClick={onDelete}
            disabled={isLoading}
          >
            Delete
          </ActionButton>
        )}
        
        {/* Custom buttons for view mode */}
        {customButtons.filter(btn => btn.mode === 'view' || !btn.mode).map((button, index) => (
          <ActionButton
            key={index}
            variant={button.variant || 'primary'}
            onClick={button.onClick}
            disabled={button.disabled || isLoading}
            loading={button.loading}
            loadingText={button.loadingText}
          >
            {button.children || button.label}
          </ActionButton>
        ))}
      </div>
    );
  }

  // Edit mode buttons
  return (
    <div className={containerClasses}>
      {/* Utility buttons group */}
      {/* <div className="flex items-center gap-2">
        {showLoadSample && (
          <ActionButton
            variant="success"
            size="default"
            onClick={onLoadSample}
            disabled={isLoading}
          >
            Load Sample
          </ActionButton>
        )}
      </div> */}

      {/* Main action buttons group */}
      <div className="flex items-center gap-2">
        <ActionButton
          variant="primary"
          onClick={onSave}
          disabled={isLoading}
          loading={isLoading}
          loadingText="Saving..."
        >
          {isLoading ? 'Saving...' : 'Save'}
        </ActionButton>

        {/* {showDirectSave && (
          <ActionButton
            variant="purple"
            onClick={onDirectSave}
            disabled={isLoading}
            loading={isLoading}
            loadingText="Saving..."
            title="Save directly to database bypassing parent form"
          >
            {isLoading ? 'Saving...' : 'Save Direct'}
          </ActionButton>
        )} */}

        <ActionButton
          variant="secondary"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </ActionButton>
      </div>

      {/* Custom buttons for edit mode */}
      {customButtons.filter(btn => btn.mode === 'edit' || !btn.mode).length > 0 && (
        <div className="flex items-center gap-2">
          {customButtons.filter(btn => btn.mode === 'edit' || !btn.mode).map((button, index) => (
            <ActionButton
              key={index}
              variant={button.variant || 'primary'}
              onClick={button.onClick}
              disabled={button.disabled || isLoading}
              loading={button.loading}
              loadingText={button.loadingText}
            >
              {button.children || button.label}
            </ActionButton>
          ))}
        </div>
      )}
    </div>
  );
};

// Export both components
export { ActionButton, ActionButtonGroup };
export default ActionButtonGroup;
