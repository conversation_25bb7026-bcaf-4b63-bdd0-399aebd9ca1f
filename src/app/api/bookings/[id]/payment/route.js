import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';


// PUT /api/bookings/[id]/payment - Update payment status (no authentication required)
export async function PUT(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    const { status, amount, currency } = body;

    if (!status) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'status is required',
        },
        { status: 400 }
      );
    }

    // Find the booking
    const booking = await Booking.findById(id)
      .populate('customer', 'firstname surname name email')
      .populate('package', 'name');

    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }

    // Update booking payment status (DPO-only / manual update)
    booking.payment.status = status;
    if (typeof amount === 'number') {
      booking.payment.amount = amount;
    }
    if (currency) {
      booking.payment.currency = currency;
    }

    if (status === 'paid') {
      booking.payment.paidAt = new Date();
      booking.status = 'confirmed';
      booking.confirmedAt = new Date();

      await booking.addCommunication({
        type: 'payment',
        direction: 'inbound',
        subject: 'Payment Completed',
        content: `Payment of ${booking.payment.amount} ${booking.payment.currency?.toUpperCase() || ''} completed via DPO.`,
        timestamp: new Date(),
      });
    } else if (status === 'failed') {
      booking.payment.failedAt = new Date();
      booking.status = 'payment_failed';

      await booking.addCommunication({
        type: 'payment',
        direction: 'inbound',
        subject: 'Payment Failed',
        content: `Payment failed for booking ${booking.bookingNumber}.`,
        timestamp: new Date(),
      });
    } else if (status === 'processing') {
      booking.status = 'payment_processing';
    }

    await booking.save();

    return NextResponse.json({
      success: true,
      data: {
        bookingId: booking._id,
        bookingNumber: booking.bookingNumber,
        paymentStatus: booking.payment.status,
        bookingStatus: booking.status,
        paidAmount: booking.payment.amount,
      },
      message: 'Payment status updated successfully',
    });
  } catch (error) {
    console.error('Error updating payment status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update payment status',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// GET /api/bookings/[id]/payment - Get payment status
export async function GET(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    
    const booking = await Booking.findById(id)
      .populate('customer', 'firstname surname name email')
      .populate('package', 'name');
    
    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: {
        bookingId: booking._id,
        bookingNumber: booking.bookingNumber,
        bookingStatus: booking.status,
        payment: {
          status: booking.payment.status,
          amount: booking.payment.amount,
          currency: booking.payment.currency,
          paidAt: booking.payment.paidAt,
        },
        totalAmount: booking.pricing.totalAmount,
      },
    });
  } catch (error) {
    console.error('Error getting payment status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get payment status',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
