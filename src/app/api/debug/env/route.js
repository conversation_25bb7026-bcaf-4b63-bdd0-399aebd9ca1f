import { NextResponse } from 'next/server';

// GET /api/debug/env - Debug environment variables (remove in production)
export async function GET() {
  try {
    const envDebug = {
      NODE_ENV: process.env.NODE_ENV,
      // Only show public env vars and whether private ones are set
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET ? 'SET' : 'NOT SET',
      MONGODB_URI: process.env.MONGODB_URI ? 'SET' : 'NOT SET',
      // DPO configuration presence
      DPO_COMPANY_TOKEN: process.env.DPO_COMPANY_TOKEN ? 'SET' : 'NOT SET',
      DPO_SERVICE_TYPE: process.env.DPO_SERVICE_TYPE ? 'SET' : 'NOT SET',
      DPO_CREATE_TOKEN_URL: process.env.DPO_CREATE_TOKEN_URL ? 'SET' : 'NOT SET',
      DPO_VERIFY_URL: process.env.DPO_VERIFY_URL ? 'SET' : 'NOT SET',
      DPO_PAYMENT_PAGE_URL: process.env.DPO_PAYMENT_PAGE_URL ? 'SET' : 'NOT SET',
      DPO_SECRET_KEY: process.env.DPO_SECRET_KEY ? 'SET' : 'NOT SET',
      // Count all env vars
      totalEnvVars: Object.keys(process.env).length,
      publicEnvVars: Object.keys(process.env).filter(key => key.startsWith('NEXT_PUBLIC_')).length
    };

    return NextResponse.json({
      success: true,
      data: envDebug,
      message: 'Environment debug info retrieved'
    });
  } catch (error) {
    console.error('Error in env debug:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Server Error',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
