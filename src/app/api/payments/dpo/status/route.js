import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
import { Payment } from '@/models/Payment';
import { assertDpoEnv, mapDpoStatus } from '@/lib/dpo';

export async function POST(request) {
  try {
    await connectDB();
    assertDpoEnv();

    const body = await request.json();
    const { token, bookingId } = body || {};

    if (!token && !bookingId) {
      return NextResponse.json({ success: false, error: 'Validation Error', message: 'token or bookingId is required' }, { status: 400 });
    }

    let payment = null;
    if (token) {
      payment = await Payment.findOne({ 'dpo.token': token }).populate('booking').populate('customer', 'email');
    } else if (bookingId) {
      payment = await Payment.findOne({ booking: bookingId, 'paymentMethod.type': 'dpo' }).populate('booking').populate('customer', 'email');
    }

    if (!payment) {
      return NextResponse.json({ success: false, error: 'Not Found', message: 'Payment not found' }, { status: 404 });
    }

    const verifyUrl = process.env.DPO_VERIFY_URL;
    const res = await fetch(verifyUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ Token: payment.dpo.token, TransactionRef: payment.dpo.transactionRef }),
      cache: 'no-store',
    });

    if (!res.ok) {
      const text = await res.text();
      throw new Error(`DPO Verify failed: HTTP ${res.status} ${res.statusText} - ${text}`);
    }

    const dpoStatus = await res.json().catch(async () => ({ raw: await res.text() }));

    const code = dpoStatus?.ResultCode || dpoStatus?.Result || dpoStatus?.Code || dpoStatus?.status;
    const message = dpoStatus?.ResultExplanation || dpoStatus?.Message || dpoStatus?.resultMessage;

    const mapping = mapDpoStatus(code);

    // Update payment
    payment.status = mapping.paymentStatus === 'succeeded' ? 'succeeded' : mapping.paymentStatus;
    payment.dpo.status = mapping.paymentStatus;
    payment.dpo.resultCode = code;
    payment.dpo.resultMessage = message;
    payment.dpo.raw = dpoStatus;
    if (mapping.paymentStatus === 'succeeded') {
      payment.processedAt = new Date();
    }
    await payment.save();

    // Update booking
    const booking = await Booking.findById(payment.booking._id);
    if (mapping.paymentStatus === 'succeeded') {
      booking.payment.status = 'paid';
      booking.payment.method = 'dpo';
      booking.payment.paidAmount = (booking.payment.paidAmount || 0) + payment.amount;
      booking.payment.remainingAmount = Math.max(0, booking.pricing.totalAmount - booking.payment.paidAmount);
    } else if (mapping.paymentStatus === 'failed') {
      booking.payment.status = 'failed';
    } else {
      booking.payment.status = 'processing';
    }
    await booking.save();

    return NextResponse.json({ success: true, data: { code, message, paymentStatus: payment.status, bookingStatus: booking.payment.status, raw: dpoStatus } });
  } catch (error) {
    console.error('DPO status error:', error);
    return NextResponse.json({ success: false, error: 'DPOStatusFailed', message: error.message }, { status: 500 });
  }
}

