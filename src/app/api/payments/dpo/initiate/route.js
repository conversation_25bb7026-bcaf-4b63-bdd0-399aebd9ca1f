import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
import { Payment } from '@/models/Payment';
import { assertDpoEnv, buildCreateTokenPayload, buildPaymentPageUrl, getBaseUrlFromRequest } from '@/lib/dpo';

export async function POST(request) {
  try {
    await connectDB();
    assertDpoEnv();

    const body = await request.json();
    const { bookingId } = body || {};

    if (!bookingId) {
      return NextResponse.json({ success: false, error: 'Validation Error', message: 'bookingId is required' }, { status: 400 });
    }

    const booking = await Booking.findById(bookingId)
      .populate('customer', 'firstname surname name email phone')
      .populate('package', 'name');

    if (!booking) {
      return NextResponse.json({ success: false, error: 'Not Found', message: 'Booking not found' }, { status: 404 });
    }

    const baseUrl = getBaseUrlFromRequest(request);
    const { payload, transactionRef } = buildCreateTokenPayload({ booking, customer: booking.customer, baseUrl });

    // Call DPO Create Token endpoint (URL must be set in env)
    const createUrl = process.env.DPO_CREATE_TOKEN_URL;

    const res = await fetch(createUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
      // Do NOT forward cookies/credentials
      cache: 'no-store',
    });

    if (!res.ok) {
      const text = await res.text();
      throw new Error(`DPO Create Token failed: HTTP ${res.status} ${res.statusText} - ${text}`);
    }

    const dpoData = await res.json().catch(async () => {
      // Some DPO endpoints may return XML; if so, require parsing adjustment
      const text = await res.text();
      return { raw: text };
    });

    // Expecting token from DPO response. Adjust the property name according to docs.
    const token = dpoData?.TransactionToken || dpoData?.PayToken || dpoData?.Token || dpoData?.token;
    if (!token) {
      return NextResponse.json({ success: false, error: 'DPOError', message: 'No token in DPO response', dpoData }, { status: 502 });
    }

    const redirectUrl = buildPaymentPageUrl({ token });

    // Create a payment record for tracking
    const payment = new Payment({
      booking: booking._id,
      customer: booking.customer._id,
      amount: booking.pricing.totalAmount,
      currency: (booking.pricing.currency || 'USD').toUpperCase(),
      paymentMethod: { type: 'dpo', details: { dpoReference: transactionRef } },
      status: 'pending',
      type: 'payment',
      dpo: {
        token,
        transactionRef,
        redirectUrl,
        status: 'pending',
        raw: dpoData,
      },
      metadata: {
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        source: 'website',
      },
      // fees/netAmount calculated by pre-save; initialize fees to 0
      fees: { totalFees: 0 },
      netAmount: booking.pricing.totalAmount,
    });

    await payment.save();

    // Mark booking payment method and set processing state
    booking.payment.method = 'dpo';
    booking.payment.status = 'processing';
    await booking.save();

    return NextResponse.json({ success: true, data: { redirectUrl, token, paymentId: payment._id } });
  } catch (error) {
    console.error('DPO initiate error:', error);
    return NextResponse.json({ success: false, error: 'DPOInitiateFailed', message: error.message }, { status: 500 });
  }
}

