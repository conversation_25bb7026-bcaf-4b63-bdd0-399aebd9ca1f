import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
import { Payment } from '@/models/Payment';
import { assertDpoEnv, mapDpoStatus } from '@/lib/dpo';

// Server-to-server notification from DPO
export async function POST(request) {
  try {
    await connectDB();
    assertDpoEnv();

    // DPO may send JSON or form-encoded; try JSON first
    let payload = null;
    try { payload = await request.json(); } catch (e) {}
    if (!payload) {
      const text = await request.text();
      // naive parse for key=value pairs
      payload = Object.fromEntries(new URLSearchParams(text));
    }

    const token = payload?.TransactionToken || payload?.PayToken || payload?.Token || payload?.token;
    const transactionRef = payload?.TransactionRef || payload?.MerchantReference || payload?.reference;
    const code = payload?.ResultCode || payload?.Result || payload?.Code || payload?.status;
    const message = payload?.ResultExplanation || payload?.Message || payload?.resultMessage;

    if (!token && !transactionRef) {
      return NextResponse.json({ success: false, error: 'Validation Error', message: 'Missing token/transactionRef' }, { status: 400 });
    }

    // Find payment
    let payment = null;
    if (token) {
      payment = await Payment.findOne({ 'dpo.token': token });
    }
    if (!payment && transactionRef) {
      payment = await Payment.findOne({ 'dpo.transactionRef': transactionRef });
    }
    if (!payment) {
      return NextResponse.json({ success: false, error: 'Not Found', message: 'Payment not found' }, { status: 404 });
    }

    const mapping = mapDpoStatus(code);

    payment.status = mapping.paymentStatus === 'succeeded' ? 'succeeded' : mapping.paymentStatus;
    payment.dpo.status = mapping.paymentStatus;
    payment.dpo.resultCode = code;
    payment.dpo.resultMessage = message;
    payment.dpo.raw = payload;
    if (mapping.paymentStatus === 'succeeded') {
      payment.processedAt = new Date();
    }
    await payment.save();

    const booking = await Booking.findById(payment.booking);
    if (mapping.paymentStatus === 'succeeded') {
      booking.payment.status = 'paid';
      booking.payment.method = 'dpo';
      booking.payment.paidAmount = (booking.payment.paidAmount || 0) + payment.amount;
      booking.payment.remainingAmount = Math.max(0, booking.pricing.totalAmount - booking.payment.paidAmount);
    } else if (mapping.paymentStatus === 'failed') {
      booking.payment.status = 'failed';
    } else {
      booking.payment.status = 'processing';
    }
    await booking.save();

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('DPO callback error:', error);
    return NextResponse.json({ success: false, error: 'DPOCallbackFailed', message: error.message }, { status: 500 });
  }
}

