import { NextResponse } from 'next/server';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('ID') || searchParams.get('Token') || searchParams.get('token');
    const bookingId = searchParams.get('bookingId');

    // Call our status endpoint to finalize
    const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || ''}/api/payments/dpo/status`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token, bookingId }),
      cache: 'no-store',
    });

    // Best effort parse
    let payload = null;
    try { payload = await res.json(); } catch (e) {}

    // Redirect user back to booking payment page with contextual state
    const redirectTo = searchParams.get('redirect') || '/';
    const target = searchParams.get('bookingPage') || `/payment/${bookingId || ''}`;

    // Prefer redirecting to payment page; attach short status params for UI
    const status = payload?.data?.paymentStatus || 'processing';
    const url = `${target}?provider=dpo&status=${encodeURIComponent(status)}`;

    return NextResponse.redirect(url);
  } catch (error) {
    console.error('DPO return error:', error);
    return NextResponse.json({ success: false, error: 'DPOReturnFailed', message: error.message }, { status: 500 });
  }
}

