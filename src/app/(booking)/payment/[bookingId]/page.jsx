'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import PaymentForm from '@/components/payments/PaymentForm';



export default function PaymentPage() {
  const params = useParams();
  const router = useRouter();
  const [booking, setBooking] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  useEffect(() => {
    fetchBooking();
  }, [params.bookingId]);

  const fetchBooking = async () => {
    try {
      const response = await fetch(`/api/bookings/${params.bookingId}`);
      const data = await response.json();

      if (data.success) {
        setBooking(data.data);
        
        // Check if payment is already completed
        if (['paid', 'completed'].includes(data.data.payment.status)) {
          setPaymentSuccess(true);
        }
      } else {
        setError(data.message || 'Failed to load booking');
      }
    } catch (err) {
      setError('Failed to load booking');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = (paymentIntent) => {
    setPaymentSuccess(true);
    setError(null); // Clear any previous errors

    // Show success message briefly before redirect
    console.log('Payment completed successfully:', paymentIntent.id);

    // Redirect to success page after a shorter delay for better UX
    setTimeout(() => {
      router.push(`/booking-confirmation/${booking._id}?payment=success&intent=${paymentIntent.id}`);
    }, 1500);
  };

  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    setError(error.message || 'Payment failed. Please try again.');
    setPaymentSuccess(false);

    // Clear error after 10 seconds to allow retry
    setTimeout(() => setError(null), 10000);
  };


  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => router.push('/booking')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
          >
            Back to Booking
          </button>
        </div>
      </div>
    );
  }

  if (paymentSuccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <div className="text-green-600 text-6xl mb-4">✓</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Payment Successful!</h1>
          <p className="text-gray-600 mb-4">
            Your booking has been confirmed. You will receive a confirmation email shortly.
          </p>
          <p className="text-sm text-gray-500">
            Redirecting to confirmation page...
          </p>
        </div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Booking Not Found</h1>
          <p className="text-gray-600 mb-4">The booking you're looking for doesn't exist.</p>
          <button
            onClick={() => router.push('/booking')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
          >
            Make New Booking
          </button>
        </div>
      </div>
    );
  }

  const customerName = booking.customer.firstname && booking.customer.surname 
    ? `${booking.customer.firstname} ${booking.customer.surname}`
    : booking.customer.name;

  return (
    <div className="h-full bg-gray-50 py-8">
      <div className='w-full h-full overflow-y-auto'>
        <div className="max-w-2xl mx-auto px-4 h-fit">
          <div className="bg-white mb-12 rounded-lg shadow-md overflow-hidden">
            {/* Header */}
            <div className="bg-blue-600 text-white p-6">
              <h1 className="text-2xl font-bold">Complete Your Payment</h1>
              <p className="text-blue-100">Booking #{booking.bookingNumber}</p>
            </div>

            {/* Booking Summary */}
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Booking Summary</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Guest</p>
                  <p className="font-medium">{customerName}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Package</p>
                  <p className="font-medium">{booking.package.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Check-in</p>
                  <p className="font-medium">
                    {new Date(booking.dates.checkIn).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Check-out</p>
                  <p className="font-medium">
                    {new Date(booking.dates.checkOut).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Nights</p>
                  <p className="font-medium">{booking.dates.duration || 1}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Amount</p>
                  <p className="font-bold text-lg text-blue-600">${booking.pricing.totalAmount}</p>
                </div>
              </div>
            </div>

            {/* Payment Form */}
            <div className="p-6 h-fit">
              <PaymentForm
                booking={booking}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
              />
            </div>

            {/* Security Notice */}
            <div className="bg-gray-50 p-4 text-center">
              <p className="text-xs text-gray-500">
                🔒 Your payment information is secure and encrypted
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
