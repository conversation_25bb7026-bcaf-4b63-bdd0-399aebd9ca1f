'use client';

import React, { useState } from 'react';
import PageTemplateInput from '@/components/PageTemplateInput';

/**
 * PageInputTestPage
 * 
 * Test page for the PageTemplateInput component.
 * Demonstrates the component functionality with sample data and callbacks.
 */

export default function PageInputTestPage() {
  const [savedData, setSavedData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showSampleData, setShowSampleData] = useState(false);

  // Enhanced form data state with additionalContent array
  const [formData, setFormData] = useState({
    title: '',
    body1: '',
    image: '',
    additionalContent: []
  });

  // Additional content management state
  const [editingContentId, setEditingContentId] = useState(null);
  const [showContentManager, setShowContentManager] = useState(false);

  // Sample data for testing
  const sampleData = {
    title: '<p style="color: white; font-family: Arial; font-size: 32px;">Welcome to Elephant Island Lodge</p>',
    body1: '<p style="color: white; font-family: Arial; font-size: 18px; line-height: 1.5;">Experience the ultimate luxury getaway at our exclusive island retreat. Nestled in pristine natural surroundings, our lodge offers unparalleled comfort and breathtaking views.</p><p style="color: white; font-family: Arial; font-size: 18px; line-height: 1.5;"><strong>Features include:</strong></p><ul style="color: white; font-family: Arial; font-size: 16px;"><li>Oceanfront accommodations</li><li>World-class dining</li><li>Adventure activities</li><li>Spa and wellness facilities</li></ul>',
    image: 'assets/art_piece_popup.png'
  };

  // Handle save callback
  const handleSave = async (islandPage) => {
    setIsLoading(true);
    
    try {
      console.log('Received islandPage object:', islandPage);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Store the saved data for display
      setSavedData({
        ...islandPage,
        savedAt: new Date().toISOString()
      });
      
      console.log('Page content saved successfully!');
      
    } catch (error) {
      console.error('Save error:', error);
      throw error; // Re-throw to let the component handle the error
    } finally {
      setIsLoading(false);
    }
  };

  // Handle cancel callback
  const handleCancel = () => {
    if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
      console.log('Form cancelled');
      // Could navigate away or reset state here
    }
  };

  // Load sample data
  const loadSampleData = () => {
    setShowSampleData(true);
  };

  // Clear sample data
  const clearSampleData = () => {
    setShowSampleData(false);
    setSavedData(null);
  };

  // Generate unique ID for content items
  const generateUniqueId = () => {
    return `content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  // Add content to additionalContent array
  const handleAddContent = (contentData) => {
    if (!contentData.title.trim() || !contentData.body1.trim()) {
      alert('Please fill in both title and body content before adding.');
      return;
    }

    const newContentItem = {
      id: generateUniqueId(),
      title: contentData.title,
      body1: contentData.body1,
      image: contentData.image || ''
    };

    setFormData(prev => ({
      ...prev,
      additionalContent: [...prev.additionalContent, newContentItem]
    }));

    console.log('Added new content item:', newContentItem);
  };

  // Edit content item
  const handleEditContent = (contentId) => {
    const contentItem = formData.additionalContent.find(item => item.id === contentId);
    if (contentItem) {
      setEditingContentId(contentId);
      // The PageTemplateInput component will be populated with this data
      console.log('Editing content item:', contentItem);
    }
  };

  // Save edited content
  const handleSaveEditedContent = (updatedContentData) => {
    if (!editingContentId) return;

    setFormData(prev => ({
      ...prev,
      additionalContent: prev.additionalContent.map(item =>
        item.id === editingContentId
          ? { ...item, ...updatedContentData }
          : item
      )
    }));

    setEditingContentId(null);
    console.log('Updated content item:', updatedContentData);
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingContentId(null);
  };

  // Delete content item
  const handleDeleteContent = (contentId) => {
    const contentItem = formData.additionalContent.find(item => item.id === contentId);
    if (contentItem && confirm(`Are you sure you want to delete "${contentItem.title.replace(/<[^>]*>/g, '').substring(0, 50)}..."? This action cannot be undone.`)) {
      setFormData(prev => ({
        ...prev,
        additionalContent: prev.additionalContent.filter(item => item.id !== contentId)
      }));
      console.log('Deleted content item:', contentId);
    }
  };

  // Get current editing data
  const getCurrentEditingData = () => {
    if (!editingContentId) return null;
    return formData.additionalContent.find(item => item.id === editingContentId);
  };

  return (
    <div className="h-full w-full bg-gray-100 py-8 overflow-y-auto">
      <div className='flex w-full h-fit'>
        <div className="max-w-6xl mx-auto px-4">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              PageTemplateInput Test Page
            </h1>
            <p className="text-gray-600 mb-6">
              This page demonstrates the PageTemplateInput component functionality with rich text editing, 
              image upload, and form validation features.
            </p>

            {/* Test Controls */}
            <div className="flex flex-wrap gap-4 mb-6">
              <button
                onClick={loadSampleData}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Load Sample Data
              </button>
              <button
                onClick={clearSampleData}
                className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
              >
                Clear Data
              </button>
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="text-sm font-medium text-blue-800 mb-2">
                Testing Instructions
              </h3>
              <div className="text-sm text-blue-700 space-y-1">
                <p>• Use the rich text editors to format title and body content</p>
                <p>• Upload an image file (JPEG, PNG, GIF, WebP - max 15MB)</p>
                <p>• Click "Show Preview" to see formatted content and generated object</p>
                <p>• Submit the form to test the save functionality</p>
                <p>• Images are uploaded to Firebase storage path: elephantisland/pages</p>
              </div>
            </div>
          </div>

          {/* Component Demo */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Component */}
            <div className="lg:col-span-2">
              <PageTemplateInput
                initialData={editingContentId ? getCurrentEditingData() : (showSampleData ? sampleData : null)}
                onSave={editingContentId ? handleSaveEditedContent : handleSave}
                onCancel={editingContentId ? handleCancelEdit : handleCancel}
                isLoading={isLoading}
                onAddContent={!editingContentId ? handleAddContent : null}
                editMode={!!editingContentId}
              />
            </div>

            {/* Sidebar with Information */}
            <div className="space-y-6">
              {/* Component Info */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-800 mb-4">
                  Component Features
                </h3>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">✓</span>
                    <span>Rich text editing with TextEditor component</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">✓</span>
                    <span>Image upload to Firebase storage</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">✓</span>
                    <span>Form validation and error handling</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">✓</span>
                    <span>Content preview functionality</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">✓</span>
                    <span>Confirmation dialogs</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">✓</span>
                    <span>Loading states and progress indicators</span>
                  </div>
                </div>
              </div>

              {/* Generated Object Display */}
              {savedData && (
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-medium text-gray-800 mb-4">
                    Last Saved Data
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Saved At:</p>
                      <p className="text-sm text-gray-600">
                        {new Date(savedData.savedAt).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Object Structure:</p>
                      <div className="bg-gray-100 p-3 rounded text-xs font-mono overflow-x-auto max-h-64 overflow-y-auto">
                        <pre>{JSON.stringify({
                          title: savedData.title,
                          body1: savedData.body1,
                          image: savedData.image
                        }, null, 2)}</pre>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* API Information */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-800 mb-4">
                  API Integration
                </h3>
                <div className="space-y-3 text-sm text-gray-600">
                  <div>
                    <p className="font-medium text-gray-700">Image Upload:</p>
                    <p>POST /api/upload/pages</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">Storage Path:</p>
                    <p>elephantisland/pages/</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">Supported Formats:</p>
                    <p>JPEG, PNG, GIF, WebP</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">Max File Size:</p>
                    <p>15MB</p>
                  </div>
                </div>
              </div>

              {/* Usage Example */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-800 mb-4">
                  Usage Example
                </h3>
                <div className="bg-gray-100 p-3 rounded text-xs font-mono overflow-x-auto">
                  <pre>{`import PageTemplateInput from '@/components/PageTemplateInput';

  <PageTemplateInput
    initialData={existingData}
    onSave={handleSave}
    onCancel={handleCancel}
    isLoading={isLoading}
  />`}</pre>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Content Management Section */}
          <div className="mt-12">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Additional Content Management</h2>
                <p className="text-gray-600 mt-1">
                  Manage multiple content items with rich text editing capabilities.
                </p>
              </div>
              <button
                onClick={() => setShowContentManager(!showContentManager)}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                {showContentManager ? 'Hide Manager' : 'Show Manager'}
              </button>
            </div>

            {showContentManager && (
              <div className="space-y-8">
                {/* Content Statistics */}
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-medium text-gray-800 mb-4">
                    Content Statistics
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {formData.additionalContent.length}
                      </div>
                      <div className="text-sm text-blue-800">Total Items</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {formData.additionalContent.filter(item => item.image).length}
                      </div>
                      <div className="text-sm text-green-800">With Images</div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">
                        {editingContentId ? '1' : '0'}
                      </div>
                      <div className="text-sm text-yellow-800">Currently Editing</div>
                    </div>
                  </div>
                </div>

                {/* Content List */}
                <div className="bg-white rounded-lg shadow-md p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-800">
                      Content Items ({formData.additionalContent.length})
                    </h3>
                    {editingContentId && (
                      <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">
                        Editing Mode Active
                      </div>
                    )}
                  </div>

                  {formData.additionalContent.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <div className="text-4xl mb-4">📝</div>
                      <p className="text-lg mb-2">No content items yet</p>
                      <p className="text-sm">Use the form above to create your first content item</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {formData.additionalContent.map((item, index) => (
                        <div
                          key={item.id}
                          className={`border rounded-lg p-4 ${
                            editingContentId === item.id
                              ? 'border-yellow-300 bg-yellow-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2 mb-2">
                                <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs font-medium">
                                  #{index + 1}
                                </span>
                                {editingContentId === item.id && (
                                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">
                                    Editing
                                  </span>
                                )}
                              </div>

                              {/* Title Preview */}
                              <div className="mb-3">
                                <h4 className="text-sm font-medium text-gray-700 mb-1">Title:</h4>
                                <div className="text-sm text-gray-900 line-clamp-2">
                                  {item.title.replace(/<[^>]*>/g, '') || 'No title'}
                                </div>
                              </div>

                              {/* Body Preview */}
                              <div className="mb-3">
                                <h4 className="text-sm font-medium text-gray-700 mb-1">Body:</h4>
                                <div className="text-sm text-gray-600 line-clamp-3">
                                  {item.body1.replace(/<[^>]*>/g, '').substring(0, 150) || 'No content'}
                                  {item.body1.replace(/<[^>]*>/g, '').length > 150 && '...'}
                                </div>
                              </div>

                              {/* Image Preview */}
                              {item.image && (
                                <div className="mb-3">
                                  <h4 className="text-sm font-medium text-gray-700 mb-1">Image:</h4>
                                  <img
                                    src={item.image}
                                    alt="Content preview"
                                    className="w-16 h-16 object-cover rounded border"
                                  />
                                </div>
                              )}
                            </div>

                            {/* Action Buttons */}
                            <div className="flex flex-col space-y-2 ml-4">
                              <button
                                onClick={() => handleEditContent(item.id)}
                                disabled={editingContentId && editingContentId !== item.id}
                                className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                {editingContentId === item.id ? 'Editing...' : 'Edit'}
                              </button>
                              <button
                                onClick={() => handleDeleteContent(item.id)}
                                disabled={!!editingContentId}
                                className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                Delete
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Additional Content Array Display */}
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-medium text-gray-800 mb-4">
                    Generated additionalContent Array
                  </h3>
                  <div className="bg-gray-100 p-4 rounded text-xs font-mono overflow-x-auto max-h-96 overflow-y-auto">
                    <pre>{JSON.stringify(formData.additionalContent, null, 2)}</pre>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="mt-12 text-center text-gray-500 text-sm">
            <p>PageTemplateInput Component Test Page</p>
            <p>Check the browser console for detailed logging information</p>
          </div>
        </div>
      </div>
    </div>
  );
}
