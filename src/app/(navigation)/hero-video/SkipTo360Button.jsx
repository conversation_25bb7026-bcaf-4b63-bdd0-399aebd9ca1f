'use client'

import { useRouter } from 'next/navigation'
import { useContextExperience } from '@/contexts/useContextExperience'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'

export default function SkipTo360Button() {
  const router = useRouter()
  const { disptachExperience } = useContextExperience()

  const onClick = () => {
    disptachExperience({
      type: ACTIONS_EXPERIENCE_STATE.SET_LANDING_PAGE_VIDEO_PLAYBACK,
      payload: true,
    })
    router.push('/360s?id=New_entrance_360_002')
  }

  return (
    <button
      onClick={onClick}
      className="fixed cursor-pointer top-5 left-1/2 transform -translate-x-1/2 z-50 flex items-center justify-center h-12 px-6 bg-black/50 hover:bg-black/70 text-white border border-white/30 rounded-full transition-all duration-300 text-sm font-medium backdrop-blur-sm"
    >
      Skip
    </button>
  )
}

