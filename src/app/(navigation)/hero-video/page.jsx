import SimpleHeroVideo from './SimpleHeroVideo';
import SkipTo360Button from './SkipTo360Button';

// Metadata for the page
export const metadata = {
  title: 'Hero Video - Elephant Island Lodge',
  description: 'Experience Elephant Island Lodge through our immersive hero video',
};

export default async function HeroVideoPage() {
  let videoPath = null;

  try {
    // Simplified API call - use relative URL to avoid fetch issues
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3001';
    const response = await fetch(`${baseUrl}/api/hero-videos/active`, {
      cache: 'no-store', // Always get fresh data
      headers: {
        'Accept': 'application/json',
      }
    });

    if (response.ok) {
      const result = await response.json();
      if (result.success && result.data?.url) {
        videoPath = result.data.url;
      }
    }
  } catch (error) {
    // Silently fall back to no video - will redirect to 360s
    console.log('No hero video available, will redirect to 360s');
  }

  return (
    <div className="relative w-full h-svh overflow-hidden bg-black">
      {/* Skip button */}
      <SkipTo360Button />

      {/* Simplified hero video component */}
      <SimpleHeroVideo videoPath={videoPath} />
    </div>
  );
}
