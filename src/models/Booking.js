import mongoose from 'mongoose';

const BookingSchema = new mongoose.Schema({
  // Booking Reference
  bookingNumber: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      // Generate booking number: EIL-YYYYMMDD-XXXX
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
      return `EIL-${year}${month}${day}-${random}`;
    }
  },
  
  // Customer Information
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  
  // Package Information
  package: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Package',
    required: true,
  },
  
  // Booking Details
  dates: {
    checkIn: {
      type: Date,
      required: true,
    },
    checkOut: {
      type: Date,
      required: true,
    },
    duration: {
      type: Number, // calculated in days
      required: true,
    },
  },
  
  // Guest Information
  guests: {
    adults: {
      type: Number,
      required: true,
      min: [1, 'At least 1 adult required'],
    },
    children: {
      type: Number,
      default: 0,
      min: [0, 'Children count cannot be negative'],
    },
    total: {
      type: Number,
      required: true,
    },
    guestType: {
      type: String,
      enum: ['individual', 'couples', 'families', 'individuals', 'singles'],
      required: true,
    },
  },
  
  // Guest Details
  guestDetails: [{
    firstname: {
      type: String,
      required: true,
    },
    surname: {
      type: String,
      required: true,
    },
    // Legacy support for single name field
    name: {
      type: String,
      required: false,
    },
    age: Number,
    dietaryRestrictions: [String],
    accessibilityNeeds: [String],
    emergencyContact: {
      firstname: String,
      surname: String,
      name: String, // Legacy support
      phone: String,
      relationship: String,
    },
  }],
  
  // Pricing
  pricing: {
    basePrice: {
      type: Number,
      required: true,
    },
    taxes: {
      type: Number,
      default: 0,
    },
    fees: {
      type: Number,
      default: 0,
    },
    discounts: {
      type: Number,
      default: 0,
    },
    totalAmount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      default: 'USD',
    },
  },
  
  // Payment Information
  payment: {
    status: {
      type: String,
      enum: ['pending', 'processing', 'partial', 'paid', 'failed', 'refunded', 'cancelled'],
      default: 'pending',
    },
    method: {
      type: String,
      enum: ['dpo', 'cash', 'bank_transfer', 'other'],
    },
    transactions: [{
      amount: Number,
      type: {
        type: String,
        enum: ['payment', 'refund', 'fee'],
      },
      status: {
        type: String,
        enum: ['pending', 'completed', 'failed'],
      },
      transactionId: String,
      date: {
        type: Date,
        default: Date.now,
      },
      notes: String,
    }],
    paidAmount: {
      type: Number,
      default: 0,
    },
    remainingAmount: {
      type: Number,
      default: 0,
    },
  },
  
  // Booking Status
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'checked_in', 'checked_out', 'cancelled', 'no_show'],
    default: 'pending',
  },
  
  // Special Requests
  specialRequests: {
    dietary: [String],
    accessibility: [String],
    transportation: String,
    other: String,
  },
  
  // Communication
  communications: [{
    type: {
      type: String,
      enum: ['email', 'sms', 'phone', 'in_person', 'note', 'payment'],
    },
    direction: {
      type: String,
      enum: ['inbound', 'outbound'],
    },
    subject: String,
    content: String,
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
    read: {
      type: Boolean,
      default: false,
    },
  }],
  
  // Check-in/Check-out
  checkInOut: {
    checkInTime: Date,
    checkOutTime: Date,
    checkInNotes: String,
    checkOutNotes: String,
    checkedInBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    checkedOutBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  
  // Cancellation
  cancellation: {
    cancelledAt: Date,
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    reason: String,
    refundAmount: {
      type: Number,
      default: 0,
    },
    refundStatus: {
      type: String,
      enum: ['none', 'pending', 'processed', 'failed'],
      default: 'none',
    },
  },
  
  // Source & Marketing
  source: {
    type: String,
    enum: ['website', 'phone', 'email', 'walk_in', 'referral', 'social_media', 'other'],
    default: 'website',
  },
  
  // Internal Notes
  internalNotes: [{
    content: String,
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    isPrivate: {
      type: Boolean,
      default: true,
    },
  }],
  
  // Metadata
  metadata: {
    ipAddress: String,
    userAgent: String,
    referrer: String,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for performance
// Note: bookingNumber index is automatically created by unique: true in schema definition
BookingSchema.index({ customer: 1 });
BookingSchema.index({ package: 1 });
BookingSchema.index({ status: 1 });
BookingSchema.index({ 'payment.status': 1 });
BookingSchema.index({ 'dates.checkIn': 1 });
BookingSchema.index({ 'dates.checkOut': 1 });
BookingSchema.index({ createdAt: -1 });

// Virtual for booking duration in nights
BookingSchema.virtual('nights').get(function() {
  if (this.dates.checkIn && this.dates.checkOut) {
    const diffTime = Math.abs(this.dates.checkOut - this.dates.checkIn);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  return 0;
});

// Virtual for remaining balance
BookingSchema.virtual('remainingBalance').get(function() {
  return this.pricing.totalAmount - this.payment.paidAmount;
});

// Pre-save middleware to generate booking number
BookingSchema.pre('save', async function(next) {
  if (this.isNew && !this.bookingNumber) {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // Find the last booking number for today
    const lastBooking = await this.constructor.findOne({
      bookingNumber: new RegExp(`^EIL${year}${month}${day}`),
    }).sort({ bookingNumber: -1 });
    
    let sequence = 1;
    if (lastBooking) {
      const lastSequence = parseInt(lastBooking.bookingNumber.slice(-3));
      sequence = lastSequence + 1;
    }
    
    this.bookingNumber = `EIL${year}${month}${day}${sequence.toString().padStart(3, '0')}`;
  }
  
  // Calculate total guests
  if (this.isModified('guests.adults') || this.isModified('guests.children')) {
    this.guests.total = this.guests.adults + this.guests.children;
  }
  
  // Calculate duration
  if (this.isModified('dates.checkIn') || this.isModified('dates.checkOut')) {
    const diffTime = Math.abs(this.dates.checkOut - this.dates.checkIn);
    this.dates.duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  
  // Update remaining amount
  if (this.isModified('payment.paidAmount') || this.isModified('pricing.totalAmount')) {
    this.payment.remainingAmount = this.pricing.totalAmount - this.payment.paidAmount;
  }
  
  next();
});

// Instance method to add communication
BookingSchema.methods.addCommunication = function(communicationData) {
  this.communications.push(communicationData);
  return this.save();
};

// Instance method to update payment status
BookingSchema.methods.updatePaymentStatus = function(status, amount = 0) {
  this.payment.status = status;
  if (amount > 0) {
    this.payment.paidAmount += amount;
    this.payment.remainingAmount = this.pricing.totalAmount - this.payment.paidAmount;
  }
  return this.save();
};

// Static method to find bookings by date range
BookingSchema.statics.findByDateRange = function(startDate, endDate) {
  return this.find({
    $or: [
      {
        'dates.checkIn': {
          $gte: startDate,
          $lte: endDate,
        },
      },
      {
        'dates.checkOut': {
          $gte: startDate,
          $lte: endDate,
        },
      },
      {
        'dates.checkIn': { $lte: startDate },
        'dates.checkOut': { $gte: endDate },
      },
    ],
  });
};

export const Booking = mongoose.models.Booking || mongoose.model('Booking', BookingSchema);
