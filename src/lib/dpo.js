import crypto from 'crypto';

// DPO helper utilities. This implementation is intentionally conservative:
// - All endpoint URLs are configured via environment variables
// - Sensitive credentials never leave the server
// - Hash/signature generation is pluggable based on docs configuration
//
// Required env vars (configure in .env.local):
// - DPO_COMPANY_TOKEN
// - DPO_SERVICE_TYPE (string or numeric as per your DPO setup)
// - DPO_ACCOUNT (optional, if your account requires it)
// - DPO_SECRET_KEY (used for hashing/signature if required)
// - DPO_CREATE_TOKEN_URL (full URL to CreatePayToken endpoint)
// - DPO_VERIFY_URL (full URL to verify/check status endpoint)
// - DPO_PAYMENT_PAGE_URL (base hosted payment page that accepts token param)
//
// See: https://docs.dpopay.com/api/index.html

export function assertDpoEnv() {
  const required = [
    'DPO_COMPANY_TOKEN',
    'DPO_SERVICE_TYPE',
    'DPO_CREATE_TOKEN_URL',
    'DPO_VERIFY_URL',
    'DPO_PAYMENT_PAGE_URL',
  ];
  const missing = required.filter((k) => !process.env[k]);
  if (missing.length) {
    throw new Error(`Missing DPO environment variables: ${missing.join(', ')}`);
  }
}

export function getBaseUrlFromRequest(request) {
  const origin = request.headers.get('origin');
  if (origin) return origin;
  const proto = request.headers.get('x-forwarded-proto') || 'https';
  const host = request.headers.get('x-forwarded-host') || request.headers.get('host');
  return `${proto}://${host}`;
}

// NOTE: Confirm exact hash specification in DPO docs and adapt as needed.
// Commonly, hash = md5(CompanyToken + ServiceType + Amount + Currency + SecretKey)
export function computeDpoHash({ companyToken, serviceType, amount, currency, secretKey }) {
  const raw = `${companyToken}${serviceType}${amount}${currency}${secretKey}`;
  return crypto.createHash('md5').update(raw).digest('hex');
}

export function buildCreateTokenPayload({ booking, customer, baseUrl }) {
  const companyToken = process.env.DPO_COMPANY_TOKEN;
  const serviceType = process.env.DPO_SERVICE_TYPE;
  const secretKey = process.env.DPO_SECRET_KEY || '';

  const amount = Number(booking?.pricing?.totalAmount || 0).toFixed(2);
  const currency = (booking?.pricing?.currency || 'USD').toUpperCase();

  const redirectUrl = `${baseUrl}/api/payments/dpo/return?bookingId=${booking?._id?.toString()}`;
  const backUrl = `${baseUrl}/api/payments/dpo/return?bookingId=${booking?._id?.toString()}`;
  const notifyUrl = `${baseUrl}/api/payments/dpo/callback`;

  const transactionRef = booking?.bookingNumber || booking?._id?.toString();

  const payload = {
    CompanyToken: companyToken,
    ServiceType: serviceType,
    PaymentAmount: amount,
    PaymentCurrency: currency,
    RedirectURL: redirectUrl,
    BackURL: backUrl,
    NotifyURL: notifyUrl,
    TransactionRef: transactionRef,
    // Optional but recommended customer fields
    CustomerEmail: customer?.email,
    CustomerPhone: customer?.phone,
    CustomerFirstName: customer?.firstname || customer?.name?.split(' ')?.[0],
    CustomerLastName: customer?.surname || customer?.name?.split(' ')?.slice(1)?.join(' '),
  };

  // If your account requires hashing/signature, include it here:
  if (secretKey) {
    payload.Hash = computeDpoHash({
      companyToken,
      serviceType,
      amount,
      currency,
      secretKey,
    });
  }

  return { payload, redirectUrl, backUrl, notifyUrl, transactionRef };
}

export function buildPaymentPageUrl({ token }) {
  const base = process.env.DPO_PAYMENT_PAGE_URL; // e.g., https://secure1.dpopay.com/pay
  const param = token ? `?ID=${encodeURIComponent(token)}` : '';
  return `${base}${param}`;
}

// Map DPO status codes to internal states. Confirm exact mapping with docs.
export function mapDpoStatus(code) {
  switch (code) {
    case '000':
    case 'APPROVED':
      return { paymentStatus: 'succeeded', bookingStatus: 'paid' };
    case '001': // pending/processing
    case 'PENDING':
      return { paymentStatus: 'processing', bookingStatus: 'processing' };
    case '999':
    case 'FAILED':
    default:
      return { paymentStatus: 'failed', bookingStatus: 'failed' };
  }
}

