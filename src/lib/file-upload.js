import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage, isFirebaseConfigured } from '@/lib/firebase';

/**
 * Upload file to Firebase Storage with graceful fallback to API route when Firebase is not configured
 */
export async function uploadFile(file, folder = 'general', filename = null) {
  try {
    // Generate filename if not provided
    if (!filename) {
      const timestamp = Date.now();
      const extension = file.name.split('.').pop();
      filename = `${timestamp}.${extension}`;
    }

    const filePath = `elephantisland/${folder}/${filename}`;

    console.log(`Uploading file: ${filePath}`);

    // If Firebase is not configured, use the server API which has a local-storage fallback
    if (!storage || !isFirebaseConfigured) {
      console.warn('Firebase Storage not configured; uploading via API fallback /api/upload/pages');
      const fd = new FormData();
      fd.append('file', file);
      const res = await fetch('/api/upload/pages', { method: 'POST', body: fd });
      const data = await res.json();
      const url = data.url || data.files?.[0]?.url || data.data?.[0]?.url;
      if (!res.ok || !url) {
        throw new Error(data.error || 'Upload failed via API fallback');
      }
      return {
        success: true,
        url,
        path: `elephantisland/${folder}/${filename}`,
        filename,
        size: file.size,
        type: file.type,
        storage: data.storage || 'api-fallback'
      };
    }

    // Try Firebase Storage directly
    const storageRef = ref(storage, filePath);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);

    return {
      success: true,
      url: downloadURL,
      path: filePath,
      filename,
      size: file.size,
      type: file.type,
      storage: 'firebase'
    };
  } catch (error) {
    console.error('File upload error:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Upload file to Firebase Storage ONLY (no local fallback)
 * Use this for 360° images that must be stored in Firebase
 */
export async function uploadFileFirebaseOnly(file, folder = 'general', filename = null) {
  try {
    // Generate filename if not provided
    if (!filename) {
      const timestamp = Date.now();
      const extension = file.name.split('.').pop();
      filename = `${timestamp}.${extension}`;
    }

    const filePath = `elephantisland/${folder}/${filename}`;

    console.log(`Uploading file to Firebase (Firebase-only mode): ${filePath}`);

    // Upload to Firebase Storage - no fallback
    const storageRef = ref(storage, filePath);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);

    console.log(`Firebase upload successful: ${downloadURL}`);

    return {
      success: true,
      url: downloadURL,
      path: filePath,
      filename,
      size: file.size,
      type: file.type,
      storage: 'firebase'
    };
  } catch (error) {
    console.error('Firebase-only upload error:', error);
    throw new Error(`Firebase upload failed: ${error.message}`);
  }
}

/**
 * Upload multiple files to Firebase Storage
 */
export async function uploadMultipleFiles(files, folder = 'general') {
  const results = [];

  for (const file of files) {
    console.log(`Processing file: ${file.name}`);
    const result = await uploadFile(file, folder);
    results.push(result);
  }

  return results;
}

/**
 * Delete file from Firebase Storage using file path
 */
export async function deleteFile(filePath) {
  try {
    if (!storage || !isFirebaseConfigured) {
      console.warn('Firebase not configured; skipping delete for path:', filePath);
      return { success: true, skipped: true, reason: 'firebase-not-configured' };
    }

    console.log(`Deleting file from Firebase: ${filePath}`);
    const storageRef = ref(storage, filePath);
    await deleteObject(storageRef);

    console.log(`File deleted successfully: ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error('Firebase deletion error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Delete file from Firebase Storage using Firebase URL
 * Extracts the file path from the Firebase URL and deletes the file
 */
export async function deleteFileByUrl(firebaseUrl) {
  try {
    console.log(`Deleting file by URL: ${firebaseUrl.substring(0, 100)}...`);

    // Check if it's a Firebase URL
    if (!firebaseUrl.includes('firebasestorage.googleapis.com')) {
      console.warn('Not a Firebase URL, skipping deletion:', firebaseUrl);
      return { success: false, error: 'Not a Firebase Storage URL' };
    }

    // If Firebase is not configured, skip deletion gracefully (prevents console errors in dev)
    if (!storage || !isFirebaseConfigured) {
      console.warn('Firebase not configured; skipping delete by URL');
      return { success: true, skipped: true, reason: 'firebase-not-configured' };
    }

    // Extract file path from Firebase URL
    // Firebase URLs format: https://firebasestorage.googleapis.com/v0/b/bucket/o/path%2Fto%2Ffile?alt=media&token=...
    const urlParts = firebaseUrl.split('/o/');
    if (urlParts.length < 2) {
      throw new Error('Invalid Firebase URL format');
    }

    const pathPart = urlParts[1].split('?')[0]; // Remove query parameters
    const filePath = decodeURIComponent(pathPart); // Decode URL encoding

    console.log(`Extracted file path: ${filePath}`);

    const storageRef = ref(storage, filePath);
    await deleteObject(storageRef);

    console.log(`File deleted successfully from URL: ${filePath}`);
    return { success: true, deletedPath: filePath };
  } catch (error) {
    console.error('Firebase URL deletion error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Validate file type and size for 360° images
 */
export function validateFile(file, options = {}) {
  const {
    maxSize = 20 * 1024 * 1024, // 20MB default for 360° images
    allowedTypes = ['image/jpeg', 'image/png', 'image/tiff'],
  } = options;

  const errors = [];

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`);
  }

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }

  // Additional validation for 360° images
  if (file.name && !file.name.match(/\.(jpg|jpeg|png|tiff)$/i)) {
    errors.push('File must have a valid image extension (.jpg, .jpeg, .png, .tiff)');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Note: createFileUploadHandler has been moved to @/lib/server-file-upload
// for server-side use only due to Node.js filesystem dependencies
