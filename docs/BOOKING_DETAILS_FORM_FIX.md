Title: Expand BookingDetailsText to Multi-field Form and Align PagesForm

Summary
- Implemented a full multi-field booking section with four fields: title, body1, body2, details.
- Updated BookingDetailsText to use local edit state for all four fields, mirroring LocationAndContactsInput patterns.
- Scoped validation inside the component and in PagesForm to the booking section with required fields (title, body1, details).
- Ensured onSave flows pass explicit section data up to PagesForm via onSectionSave('booking', data) and also synchronize parent form state via onQuillChange for each field.
- Improved UX: separate editors for each field, selection-based line-height tools apply to details editor, with per-field error display.
- Added sample and clear content handlers to set/clear all four fields consistently.

Key Changes
1) src/components/pages/BookingDetailsText.jsx
- localFormData expanded to { title, body1, body2, details }.
- useEffect sync updated to reflect four-field structure.
- Added refs for each editor; line-height selection tracking and application now target the details editor.
- handleLoadSampleContent and handleClearContent updated to affect all fields.
- handleSave and handleDelete now pass explicit section data to parent: onSectionSave('booking', localFormData/emptyData).
- View mode renders four stacked panels (Title, Body 1, Body 2, Details) with preserved line-height styling.
- Error display shows per-field messages for title, body1, body2 (optional), and details.
- Internal validateBookingDetails now requires title, body1, and details.

2) src/components/pages/PagesForm.jsx
- booking structure expanded to { title, body1, body2, details } in initial state and when mapping pages prop.
- validateForm updated to validate booking.title, booking.body1, and booking.details.
- BookingDetailsText integration already passes through onQuillChange and onSectionSave; child now calls onSectionSave('booking', data).

Behavioral Notes
- Editing uses local state; Save updates parent form state (onQuillChange) and persists via onSectionSave.
- Cancel reverts local state to current parent form values.
- Delete clears all four booking fields and saves the empty object.
- Line-height tooling remains available and is scoped to the Details editor for targeted formatting.

Developer Testing Notes
- Switch to the Booking tab in PagesForm.
- Click Edit, type into all four editors, Save. Changes should reflect immediately and persist.
- Attempt saving with empty title/body1/details to see validation errors.
- Use Load Sample Content to quickly populate all four fields, then Save.
- Clear Content to reset all four fields.

Suggested Commit Message
feat(pages): expand BookingDetailsText to support title/body1/body2/details and align PagesForm validation

- Add multi-field local state and editors (title, body1, body2, details)
- Pass explicit booking data on save/delete; sync via onQuillChange
- Update PagesForm booking structure and scoped validation
- Improve error display and UX; sample/clear handlers cover all fields

