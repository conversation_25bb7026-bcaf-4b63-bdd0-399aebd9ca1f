/**
 * Test Script: Article Popup Fixes Verification
 * 
 * This script verifies that the article popup fixes are working correctly
 * by testing the complete flow from marker click to popup display.
 * 
 * Usage: Run this in the browser console on a 360° viewer page
 */

function testArticlePopupFixes() {
  console.log('🧪 Testing Article Popup Fixes...');
  
  // Test 1: Check if the fixes are in place
  console.log('\n📋 Test 1: Verifying Fix Implementation');
  
  // Check if enhanced logging is working
  const originalConsoleLog = console.log;
  let logCount = 0;
  console.log = function(...args) {
    if (args[0] && typeof args[0] === 'string' && 
        (args[0].includes('🎯') || args[0].includes('🚀') || args[0].includes('📄'))) {
      logCount++;
    }
    return originalConsoleLog.apply(console, args);
  };
  
  // Test 2: Simulate marker click
  console.log('\n📋 Test 2: Simulating Article Marker Click');
  
  // Look for article markers in the DOM
  const markers = document.querySelectorAll('[data-marker-type="infoDoc"], .marker-icon');
  console.log(`Found ${markers.length} potential markers`);
  
  if (markers.length > 0) {
    console.log('🎯 Clicking first available marker...');
    markers[0].click();
    
    // Wait a moment for state changes
    setTimeout(() => {
      console.log = originalConsoleLog; // Restore console.log
      
      // Test 3: Verify popup state
      console.log('\n📋 Test 3: Verifying Popup State');
      
      const videoPopup = document.querySelector('.VideoPopupWrapper');
      const generalPopup = document.querySelector('.GeneralPopupWrapper');
      const itemInfo = document.querySelector('.ItemInfoComponent, [class*="ItemInfo"]');
      
      console.log('🔍 DOM Elements Found:');
      console.log('  VideoPopupWrapper:', !!videoPopup);
      console.log('  GeneralPopupWrapper:', !!generalPopup);
      console.log('  ItemInfoComponent:', !!itemInfo);
      
      // Test 4: Analyze results
      console.log('\n📋 Test 4: Results Analysis');
      
      if (generalPopup && !videoPopup) {
        console.log('✅ SUCCESS: GeneralPopupWrapper is showing (correct for article)');
      } else if (videoPopup && !generalPopup) {
        console.log('❌ ISSUE: VideoPopupWrapper is showing (should be GeneralPopupWrapper)');
      } else if (videoPopup && generalPopup) {
        console.log('⚠️ WARNING: Both popups are showing (potential conflict)');
      } else {
        console.log('❓ INFO: No popups showing (marker might not be article type)');
      }
      
      if (itemInfo) {
        console.log('✅ SUCCESS: ItemInfoComponent is present');
      } else {
        console.log('❌ ISSUE: ItemInfoComponent not found');
      }
      
      console.log(`📊 Enhanced logging triggered ${logCount} times`);
      
      // Test 5: State validation
      console.log('\n📋 Test 5: State Validation');
      
      // Try to access React state (if available)
      try {
        const reactRoot = document.querySelector('#__next');
        if (reactRoot && reactRoot._reactInternalFiber) {
          console.log('🔍 React state inspection available');
        } else {
          console.log('ℹ️ React state not directly accessible (normal in production)');
        }
      } catch (error) {
        console.log('ℹ️ React state inspection not available');
      }
      
      // Final summary
      console.log('\n🎉 Test Complete!');
      console.log('\n📊 Summary:');
      console.log('  - Enhanced logging:', logCount > 0 ? '✅ Working' : '❌ Not working');
      console.log('  - Popup display:', generalPopup && !videoPopup ? '✅ Correct' : '❌ Incorrect');
      console.log('  - Article content:', itemInfo ? '✅ Loading' : '❌ Missing');
      
      return {
        success: generalPopup && !videoPopup && itemInfo,
        enhancedLogging: logCount > 0,
        popupState: {
          video: !!videoPopup,
          general: !!generalPopup,
          itemInfo: !!itemInfo
        },
        logCount
      };
      
    }, 1000); // Wait 1 second for state changes
    
  } else {
    console.log('❌ No markers found. Make sure you\'re on a 360° page with article markers.');
    console.log('💡 Try navigating to: /360s?id=entrance_360');
    return { success: false, reason: 'No markers found' };
  }
}

// Helper function to test specific marker types
function testMarkerType(markerType) {
  console.log(`🧪 Testing ${markerType} markers...`);
  
  const markers = document.querySelectorAll(`[data-marker-type="${markerType}"]`);
  console.log(`Found ${markers.length} ${markerType} markers`);
  
  if (markers.length > 0) {
    console.log(`🎯 Clicking first ${markerType} marker...`);
    markers[0].click();
    
    setTimeout(() => {
      const videoPopup = document.querySelector('.VideoPopupWrapper');
      const generalPopup = document.querySelector('.GeneralPopupWrapper');
      
      console.log(`📊 ${markerType} Results:`);
      console.log('  VideoPopupWrapper:', !!videoPopup);
      console.log('  GeneralPopupWrapper:', !!generalPopup);
      
      // Expected results by marker type
      const expected = {
        'infoDoc': { general: true, video: false },
        'infoVideo': { general: false, video: true },
        'infoImage': { general: true, video: false }
      };
      
      const exp = expected[markerType];
      if (exp) {
        const correct = (!!generalPopup === exp.general) && (!!videoPopup === exp.video);
        console.log(`  Result: ${correct ? '✅ Correct' : '❌ Incorrect'}`);
      }
      
    }, 1000);
  }
}

// Helper function to monitor state changes
function monitorStateChanges(duration = 10000) {
  console.log(`🔍 Monitoring state changes for ${duration}ms...`);
  
  let lastVideoState = null;
  let lastGeneralState = null;
  let changeCount = 0;
  
  const interval = setInterval(() => {
    const videoPopup = !!document.querySelector('.VideoPopupWrapper');
    const generalPopup = !!document.querySelector('.GeneralPopupWrapper');
    
    if (videoPopup !== lastVideoState || generalPopup !== lastGeneralState) {
      changeCount++;
      console.log(`🔄 State Change #${changeCount}:`, {
        video: videoPopup,
        general: generalPopup,
        timestamp: new Date().toISOString()
      });
      
      lastVideoState = videoPopup;
      lastGeneralState = generalPopup;
    }
  }, 100); // Check every 100ms
  
  setTimeout(() => {
    clearInterval(interval);
    console.log(`📊 Monitoring complete. Detected ${changeCount} state changes.`);
  }, duration);
  
  return interval;
}

// Instructions
console.log(`
🧪 Article Popup Fixes Test Suite

Available Functions:
1. testArticlePopupFixes() - Run complete test suite
2. testMarkerType('infoDoc') - Test specific marker type
3. monitorStateChanges(10000) - Monitor state changes for 10 seconds

To run the main test:
testArticlePopupFixes()

Make sure you're on a 360° page with article markers before testing.
`);

// Export functions to global scope
window.testArticlePopupFixes = testArticlePopupFixes;
window.testMarkerType = testMarkerType;
window.monitorStateChanges = monitorStateChanges;
