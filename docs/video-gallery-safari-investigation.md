Video Gallery: Safari Playback Failure Investigation and Fixes

Summary (use for commit message)
- Fix: Ensure VideoGalleryComponent renders VideoPlayer only when valid videoData is available
- Fix: Correct data-fetch error handling and loading state; show spinner properly while loading
- Add: Robust diagnostics/logging for Safari, data flow, and render conditions
- Add: Safari detection and hidden debug DOM hook for QA
- Add: Render VideoPlayer through a body-level portal to avoid Safari stacking/position issues with fixed overlays
- Improve: Single-video selection logic and guard against invalid data

What we investigated
- Data flow from /api/video-gallery -> VideoGalleryComponent state -> VideoPlayer props
- Conditions that mount the VideoPlayer in both "single video" and gallery contexts
- Potential Safari-specific rendering issues where no <video> element appears at all

Root causes identified
1) VideoPlayer not mounting when expected due to conditional rendering that didnt validate the incoming data. An empty object `{}` is truthy, masking missing/invalid fields like `url`.
2) Incorrect error handling in fetchData: checking the component's `data` state rather than the response, causing spurious errors and skipping proper loading UI.
3) Lack of diagnostics to quickly confirm state/conditions in Safari environments.

Changes in src/components/menu-popup/VideoGalleryComponent.jsx
- Data fetching
  - Reworked fetchData():
    - <PERSON><PERSON><PERSON> parses response and checks `responseData.data` array length
    - Sets error/showError only when needed; uses `finally` to set loading=false
  - Spinner condition now checks actual data presence: `loading && (!Array.isArray(data) || data.length === 0)`
- Data validation
  - Added isValidVideoData() to verify required props exist: `url` and `title`
  - Rendering of VideoPlayer now requires isValidVideoData(videoData)
- Selection logic
  - handleSingleVideoClick(): matches by title or _id from experienceState.showVideoInfo.id
  - Sets showVideoPlayer true only when a valid matching item is found; resets if not
- Safari and diagnostics
  - isSafari detection via UA; logs environment on mount
  - Console logs when data loads, when videoData changes, and when mount conditions are evaluated
  - Hidden debug DOM element to probe state in QA tools:
    - <div class="sr-only" data-testid="video-debug" data-safari data-valid data-url="..." />
- Gallery click
  - handleVideoClick() logs clicked item and triggers single-video-in-gallery state

How to verify (manual)
- Open the gallery page in Safari (iOS and macOS)
  - Confirm that clicking a thumbnail logs a "gallery item clicked" message with title and url
  - Confirm that a subsequent log shows render conds with condGallery=true
  - Check the DOM for [data-testid="video-debug"] and verify data-safari=true on Safari and data-valid=true once a valid item is selected
  - Ensure the VideoPlayer appears; with the previously updated VideoPlayer.jsx, Safari should play inline and honor autoplay policies properly
- Single video via context
  - Trigger experienceState.showVideoInfo.id to one of the items (by title or _id)
  - Confirm logs show a matched video and render condSingle=true
- Loading and errors
  - On slow network, observe spinner while loading
  - If /api/video-gallery returns empty, see a console error and showError=true in state (UI can optionally display this if desired)

Notes and limitations
- We only render VideoPlayer when the data object is complete enough to avoid silent failures (at minimum: url + title). If your API doesnt provide `title`, update isValidVideoData accordingly.
- If the video URL is cross-origin, ensure CORS headers at the origin (see the VideoPlayer Safari doc) to avoid loading issues.
- For maximum Safari compatibility, ensure MP4 (H.264/AAC) assets are provided. WebM may fail on older Safari versions.

Files touched
- src/components/menu-popup/VideoGalleryComponent.jsx

Commit message suggestion
Fix: Make VideoGallery mount VideoPlayer with valid data and add Safari diagnostics
- Validate videoData before rendering player; improve selection logic
- Correct fetchData error/loader behavior and add detailed logs
- Add Safari detection and hidden debug DOM hook for QA

