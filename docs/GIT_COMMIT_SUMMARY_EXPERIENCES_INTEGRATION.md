# Git Commit Summary — Integrate IslandAndInfoMarkerInput for Experiences

## Summary
- Integrated IslandAndInfoMarkerInput into the Experiences section of PagesForm
- Generalized IslandAndInfoMarkerInput to support multiple sections via a `section` prop
- Ensured experiences data (`pages.experiences`) initializes correctly and saves independently
- Preserved state isolation to avoid cross-section data corruption
- Maintained consistent UI/UX, including rich text editors and image uploads

## Files Changed
- src/components/IslandAndInfoMarkerInput.jsx
- src/components/pages/PagesForm.jsx

## Key Changes
- Added `section` and optional `heading` props to IslandAndInfoMarkerInput (default `section='island'`)
- Initialization now reads `pages[section]` instead of only `pages.island`
- Save handler now calls `onSave({ [section]: data })` to update only the targeted section
- Dynamic UI labels (header and save button) adapt to the current section
- Wired PagesForm experiences tab to render IslandAndInfoMarkerInput with `section="experiences"`
- Removed old ExperiencesSection import/usage

## Behavior
- Island tab uses IslandAndInfoMarkerInput (unchanged behavior)
- Experiences tab now uses the same rich interface
- Users can create, edit, and save experiences content independently
- Edits to additional content immediately reflect in the UI after save

## Commit Message
```
feat(pages): integrate IslandAndInfoMarkerInput for experiences; generalize component for multi-section use

- Generalize IslandAndInfoMarkerInput to accept `section` (default 'island') and dynamic heading
- Initialize from pages[section] and save via onSave({ [section]: data })
- Replace ExperiencesSection with IslandAndInfoMarkerInput in PagesForm
- Maintain state isolation and immediate UI updates on additional content edits
```

