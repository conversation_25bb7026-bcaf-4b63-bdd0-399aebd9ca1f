Title: Fix backend submission for BookingDetailsText multi-field booking data

Summary
- Root cause: MongoDB schema (BookingPageSchema) only allowed `details`. Mongoose dropped `title`, `body1`, `body2` on save, so backend stored only `details`.
- Fix: Expanded BookingPageSchema to include `title`, `body1`, `body2`, `details`. Updated defaults, fallbacks, and route safeguards to initialize booking with all four fields.
- Verified: API routes now accept and persist all four fields when submitting `{ section: 'booking', data: { title, body1, body2, details } }` to POST /api/pages. GET /api/pages returns the full structure.

Changes
1) src/models/Page.js
- Updated BookingPageSchema:
  - Added `title` (String, required)
  - Added `body1` (String, required)
  - Added `body2` (String, optional, default '')
  - Kept `details` (String, required)
- Updated initializeDefaultPages() booking defaults to include all four fields.

2) src/app/api/pages/route.js
- Updated FALLBACK_PAGES_DATA.booking to include all four fields.
- When ensuring booking exists in POST handler, initialize with all four fields.

3) src/app/api/pages/[id]/route.js
- When ensuring booking exists in PUT handler, initialize with all four fields.

Why this works
- With the schema updated, Mongoose now allows and persists `title`, `body1`, `body2` alongside `details` under `pages.booking`.
- Fallback and initialization paths prevent older databases from missing the expanded shape, avoiding runtime null errors.

Manual testing steps
1. In the UI (PagesForm → Booking): enter values for title, body1, body2, details; click Save.
2. Observe POST /api/pages payload includes all four fields.
3. On success, GET /api/pages should return `booking` with title/body1/body2/details.
4. If DB is unconfigured, GET uses fallback; POST requires DB connection. Configure MongoDB URI for end-to-end verification.

Suggested commit message
fix(pages): persist full booking object (title/body1/body2/details) in backend

- Expand BookingPageSchema to include title/body1/body2
- Initialize/ fallback booking with all fields in API routes
- Ensure POST/PUT persist the full booking structure
- Align backend with updated BookingDetailsText and PagesForm

