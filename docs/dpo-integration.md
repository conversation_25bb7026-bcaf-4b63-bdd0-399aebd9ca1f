# DPO Payment Integration (DPO-only)

This app now uses DPO Pay as the sole payment method. All Stripe code, routes, and environment variables have been removed. This document explains the DPO-only flow, configuration, and operational notes.

## What’s included

- Server utilities: `src/lib/dpo.js`
  - Validates required env vars
  - Builds CreatePayToken payload and optional MD5 hash
  - Maps DPO status/result codes to internal statuses
  - Builds hosted payment page URL
- API routes (App Router):
  - `POST /api/payments/dpo/initiate` — create DPO token and return hosted payment page URL
  - `POST /api/payments/dpo/status` — verify status with DPO and update DB
  - `GET /api/payments/dpo/return` — user return URL (calls status then redirects)
  - `POST /api/payments/dpo/callback` — DPO IPN (server-to-server) handler
- Models:
  - `src/models/Payment.js`: removed Stripe fields and indexes; retained/expanded `dpo` sub-doc; neutral fee fields
  - `src/models/Booking.js`: `payment.method` enum no longer includes `stripe`; removed `stripePaymentIntentId`, `stripeCustomerId`
- UI:
  - `src/components/payments/PaymentForm.jsx`: simplified to DPO-only; single CTA redirects to DPO
  - `src/app/(booking)/payment/[bookingId]/page.jsx`: uses DPO-only PaymentForm and updated copy
- Diagnostics:
  - `src/app/api/debug/env/route.js`: shows DPO env presence (removed Stripe previews)

## Environment variables (required)
Add to `.env.local` (never commit secrets):

```
DPO_COMPANY_TOKEN=your_company_token
DPO_SERVICE_TYPE=your_service_type
DPO_SECRET_KEY=optional_secret_if_required
DPO_CREATE_TOKEN_URL=https://<secure-dpo-host>/api/create-token   # confirm with DPO docs
DPO_VERIFY_URL=https://<secure-dpo-host>/api/verify-token        # confirm with DPO docs
DPO_PAYMENT_PAGE_URL=https://<secure-dpo-host>/pay                # confirm with DPO docs
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

Cleanup (remove from all environments):
- STRIPE_SECRET_KEY
- NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
- STRIPE_WEBHOOK_SECRET

## Security
- All DPO credentials are used server-side only
- No payment credentials handled on the client; users are redirected to DPO hosted page
- IPN handler updates Booking/Payment in a single, consistent flow
- Hash/signature supported via `DPO_SECRET_KEY` when required (adapt per account)

## User flow
1. User lands on `/payment/[bookingId]` and sees booking summary
2. Click “Continue to DPO” — we call `POST /api/payments/dpo/initiate`
3. Browser redirects to DPO hosted payment page
4. After payment, DPO sends user to `/api/payments/dpo/return?bookingId=...` and also POSTs to `/api/payments/dpo/callback`
5. Our server verifies status with DPO and updates MongoDB
6. UI reflects the latest status when the user returns

## Testing
- End-to-end (sandbox):
  1) Create a booking
  2) Go to `/payment/[bookingId]` and click “Continue to DPO”
  3) Complete sandbox payment
  4) Confirm booking status becomes `confirmed` and payment becomes `paid` (or mapped equivalent)
- IPN simulation: POST a sample payload to `/api/payments/dpo/callback` containing `Token`, `TransactionRef`, and a `ResultCode` like `000`
- Status check: `POST /api/payments/dpo/status` with `token` or `bookingId`

## Migration notes (from Stripe to DPO)
- Deleted code: all Stripe API routes, webhook, and helpers
- Removed Stripe fields from Booking and Payment schemas
- Updated UI to remove provider selection and any Stripe copy
- Updated env debug route and documentation

## Commit message (summary)

feat(payments): migrate to DPO-only payment flow and remove Stripe

- Remove all Stripe code, routes, models, and environment references
- Keep DPO hosted redirect flow: initiate, status, return, callback (IPN)
- Update Booking/Payment schemas to drop Stripe fields and indexes
- Simplify PaymentForm to DPO-only CTA; update payment page copy
- Add DPO env presence to /api/debug/env; update docs

BREAKING CHANGE: Stripe configuration removed; requires DPO env vars

