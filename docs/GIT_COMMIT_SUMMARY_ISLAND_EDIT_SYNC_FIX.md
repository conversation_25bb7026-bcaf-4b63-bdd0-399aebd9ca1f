# Git Commit Summary — IslandAndInfoMarkerInput Edit Synchronization Fix

## Summary
- Fixed regression where edits to additional content items were not reflected in UI after saving
- Prevented props-driven state reset from clobbering local edits after PagesForm integration
- Ensured image upload/removal during edit mode updates `editFormData` (source of truth) and persists on save
- Improved React re-render reliability for additional items by using a more stable key
- Avoided empty image src warnings by conditionally rendering the main image

## Files Touched
- src/components/IslandAndInfoMarkerInput.jsx

## Technical Notes
- Added `hasInitializedRef` and guarded pages->state initialization to run on first load (or when local state is empty), avoiding unintended resets during edit workflow
- `handleAdditionalClick` immutably updates `additionalFormData[editingIndex]` and exits edit mode cleanly
- `uploadImage` and `handleImageRemove` now update `editFormData` in edit mode; main form unaffected
- Additional items use composite key `${index}-${item.title}-${item.body1}-${item.image}` to force correct re-rendering when content changes

## Commit Message
```
fix(island): preserve edited additional content after save; guard props->state init

- Guard pages-based initialization to avoid clobbering local edits after PagesForm integration
- Ensure image upload/removal in edit mode updates editFormData (source of truth)
- Immutably update additionalFormData item on Save Changes and exit edit mode
- Improve re-render reliability with composite key for additional items
- Avoid empty img src warnings by conditional rendering
```

