# Commit Summary: DPO-only Payment Migration

## Conventional Commit

feat(payments): migrate to DPO-only payment flow and remove Stripe

- Remove all Stripe code, routes, models, and environment references
- Keep DPO hosted redirect flow: initiate, status, return, callback (IPN)
- Update Booking/Payment schemas to drop Stripe fields and indexes
- Simplify PaymentForm to DPO-only CTA; update payment page copy
- Add DPO env presence to /api/debug/env; update docs

BREAKING CHANGE: Stripe configuration removed; requires DPO env vars

## Files Changed

- Modified
  - src/app/(booking)/payment/[bookingId]/page.jsx (remove Stripe mentions; use DPO-only PaymentForm)
  - src/app/api/bookings/[id]/payment/route.js (remove Stripe intent lookups from GET; DPO-only fields)
  - src/components/payments/PaymentForm.jsx (DPO-only button; Tailwind styles)
  - src/lib/dpo.js (env validation, payload construction, mapping)
  - src/app/api/debug/env/route.js (show DPO env; remove Stripe env preview)
  - docs/dpo-integration.md (updated to DPO-only)

- Deleted (Stripe)
  - src/app/api/payments/confirm/route.js
  - src/app/api/payments/create-intent/route.js
  - src/app/api/payments/methods/route.js
  - src/app/api/payments/route.js
  - src/app/api/payments/webhook/route.js
  - src/lib/stripe.js

- Models
  - src/models/Payment.js: remove `stripe` sub-doc; remove Stripe indexes; remove `refund.refundId`; keep DPO sub-doc
  - src/models/Booking.js: remove `stripePaymentIntentId`, `stripeCustomerId`; method enum now excludes `stripe`

## Env Changes

Remove these from all environments:
- STRIPE_SECRET_KEY
- NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
- STRIPE_WEBHOOK_SECRET

Ensure these are set:
- DPO_COMPANY_TOKEN
- DPO_SERVICE_TYPE
- DPO_SECRET_KEY (if your account requires hashing)
- DPO_CREATE_TOKEN_URL
- DPO_VERIFY_URL
- DPO_PAYMENT_PAGE_URL
- NEXT_PUBLIC_APP_URL

## Testing Notes

- E2E: create booking -> payment page -> redirect to DPO -> complete -> return -> booking status updates
- IPN: send test payload to /api/payments/dpo/callback (Token, TransactionRef, ResultCode)
- Status: POST /api/payments/dpo/status with token or bookingId

