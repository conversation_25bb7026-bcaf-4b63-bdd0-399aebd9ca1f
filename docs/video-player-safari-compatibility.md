Video Player: Safari and Cross‑Browser Compatibility Notes

Summary (use for commit message)
- Fix: Make VideoPlayer work reliably on Safari (desktop/mobile) and other major browsers
- Add: playsInline, webkit-playsinline, muted handling, preload, crossOrigin, disablePictureInPicture/disableRemotePlayback
- Improve: Robust play() with Safari autoplay fallback (retry muted), better buffering states
- Add: Format detection to surface helpful error for unsupported types (e.g., WebM on older Safari)
- Add: Touch support for progress bar seek and controls reveal on mobile
- Docs: Browser-specific considerations, formats, and troubleshooting

What changed in src/components/menu-popup/VideoPlayer.jsx
- Attributes added to <video> to maximize Safari compatibility:
  - playsInline and webkit-playsinline to prevent forced fullscreen on iOS Safari and enable inline playback
  - muted driven by state and synchronized with video.muted (Safari policies rely on this flag)
  - preload="metadata" to improve startup while avoiding unnecessary heavy loads
  - crossOrigin="anonymous" to prevent CORS-related playback issues when videos are served from another domain
  - disablePictureInPicture and disableRemotePlayback to avoid feature conflicts on some devices
- Playback logic:
  - togglePlayPause now awaits video.play() and catches errors
  - On failure (e.g., Safari blocking), it retries with video.muted = true and sets UI state accordingly
  - If a second attempt fails, a friendly message prompts the user to tap to play
- Volume/mute logic:
  - Volume slider updates video.volume and video.muted together; mute button flips video.muted explicitly
  - Video onVolumeChange keeps React state in sync with hardware/OS changes
- Format compatibility detection:
  - New useEffect checks canPlayType against the file extension (mp4, webm, mov)
  - If the browser can’t play the detected type, we set an error with a helpful message (e.g., suggest MP4/H.264)
- Mobile interactions:
  - Progress bar now supports touch start/move for seeking on iOS/Android
  - Touch start on the container reveals controls like mouse movement does on desktop

Why these changes help Safari
- Autoplay and user gesture policy: Safari (especially iOS) often blocks play() if not user-initiated, or if audio is present. Ensuring playsInline and properly toggling muted increases the chance of successful playback. We wrap play() in a try/catch and retry muted when necessary.
- Forced fullscreen on iOS Safari: playsInline + webkit-playsinline allows inline playback within our custom UI layout.
- Format support: Apple devices historically didn’t support WebM. MP4 (H.264 video + AAC audio) has the broadest compatibility. The new detection warns users early if the provided format is not supported.
- CORS: When videos are hosted on a different domain, Safari can be stricter. crossOrigin="anonymous" paired with correct server CORS headers prevents subtle loading or error events without obvious messages.

Recommended video formats and encoding
- Use MP4 container with H.264 video and AAC audio. This is the safest cross‑browser option.
- Provide a single high-compatibility URL when possible, or host parallel encodes if you must serve WebM for certain cases.
- If you plan to support HLS (m3u8) for adaptive streaming, be aware that extra player logic or libraries may be needed.

Server/CORS checklist (if using a CDN or separate domain)
- Send Access-Control-Allow-Origin: * (or your site origin) for the video URL
- Send Access-Control-Allow-Headers as needed
- Confirm that range requests are allowed (Accept-Ranges: bytes) so browsers can seek efficiently

Manual test matrix (quick checklist)
- Safari iOS (iPhone/iPad):
  - Plays inline without jumping to fullscreen
  - Tapping big play button starts playback
  - When blocked initially, a subsequent tap works; controls show a helpful message if needed
  - Progress scrubbing via touch works and updates the time
  - Volume/mute reflect hardware changes
- Safari macOS:
  - Inline playback with mouse/keyboard controls, seeking, and buffering states
- Chrome, Firefox, Edge (desktop):
  - Basic play/pause, seek, mute/volume, buffering spinner and error overlay
  - Progress bar scrubbing and keyboard shortcuts (Space/Arrow keys/M/F)
- Android Chrome/Firefox:
  - Tap to play works
  - Seek via touch and controls overlay visibility

Known limitations / considerations
- If you only provide WebM, older Safari versions may not play it. Provide MP4 (H.264/AAC) for widest support.
- crossOrigin only helps if the server sends permissive CORS headers. Otherwise playback may fail or be unreliable.
- Autoplay of audible media without a user gesture will always be restricted. We handle this gracefully by retrying muted, but fully automatic audible autoplay is intentionally limited by browsers.
- Custom controls are used; native control behaviors (like default double-tap gestures) may differ from system players.

Troubleshooting tips
- Check the browser console for MediaError or NotAllowedError messages after play() attempts
- Verify MIME types at the origin (e.g., Content-Type: video/mp4 for .mp4)
- Confirm the media’s codecs using a tool like ffprobe; ensure H.264 Baseline/Main/High profile + AAC LC
- Test the video URL directly in the browser address bar to see raw player behavior and network headers

File touched
- src/components/menu-popup/VideoPlayer.jsx

Commit message suggestion
Fix: Safari-compatible VideoPlayer
- Add playsInline/webkit-playsinline, muted handling, preload, crossOrigin
- Implement play() try/catch with muted retry for Safari autoplay policy
- Sync volume/muted state and handle onVolumeChange
- Add touch-based seeking and controls reveal on mobile
- Detect unsupported formats and show helpful error message

