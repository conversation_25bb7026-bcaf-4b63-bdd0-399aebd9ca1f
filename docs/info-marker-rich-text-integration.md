# Info Marker Rich Text Preservation

This document describes the fix to ensure rich text formatting (bold, italic, lists, links, etc.) is preserved throughout the Info Marker flow: creation → storage → retrieval → editing.

## Summary of Changes

- Preserve original HTML when editing existing info markers by passing unmodified records to the form
- Keep using cleaned/plain text only for list display and previews
- Confirmed TextEditor integration captures and returns HTML content for title, body1, body2, and secondary entry fields
- Verified API endpoints and Mongoose model store and return HTML strings intact

## Affected Files

- Updated: `src/components/info-markers/InfoMarkerList.jsx`
  - New state `rawInfoMarkers` to keep original records from the API
  - Use cleaned/plain text only for the displayed list items
  - When user clicks Edit, pass the raw item (with HTML) to the form
- Verified: `src/components/info-markers/InfoMarkerForm.jsx`
  - Uses `TextEditor` which returns `innerHTML` so formatting is captured
  - Loads values directly from `infoMarker` prop (now raw as provided by list)
  - Validation strips tags only to check emptiness; submission sends original HTML
- Verified: `src/components/info-markers/InfoMarkerManagement.jsx`
  - For create/update, forwards form data as JSON to API with formatting intact
- Verified: API
  - `src/app/api/info-markers/route.js` (POST)
  - `src/app/api/info-markers/[id]/route.js` (GET/PUT/PATCH)
  - Store and return strings as-is; no sanitization that would strip HTML
- Verified: Model
  - `src/models/InfoMarker.js` defines string fields; HTML is stored intact

## Implementation Details

1. Preserve raw HTML for editing
   - `InfoMarkerList.jsx` fetch now:
     - `setRawInfoMarkers(data.data)` to keep original, unmodified items
     - `setInfoMarkers(cleanTextFieldsArray(data.data))` strictly for display
   - On Edit button:
     - Find raw record by `_id` in `rawInfoMarkers` and pass to `onEdit(raw)`

2. Editor integration
   - `TextEditor` component uses `contentEditable` and `innerHTML` on change
   - In `InfoMarkerForm.jsx`, the `onChange` handlers receive HTML and store in `formData`
   - Submit sends `formData` as-is to API to preserve HTML

3. Secondary entries
   - Editing a secondary entry pulls values from `formData.secondaryEntries` (now populated with raw HTML from the parent item) and sends unchanged HTML back on submit or update

## How It Works Now

- Create: Form captures HTML via `TextEditor` and submits unchanged to API → stored with formatting
- List: API returns raw HTML; list stores it in `rawInfoMarkers` and displays only a cleaned/plain-text preview in the table
- Edit: Clicking Edit uses the raw record so the form loads rich HTML back into `TextEditor`
- Update: Submitting the form sends the HTML unchanged and updates the DB

## Notes / Consistency

- This approach follows patterns used across other components that leverage `TextEditor` and `HtmlContentDisplay` for rendering
- Only use `cleanTextFields` for display/truncation, never for data used in editors or persistence

## Manual Test Plan

1. Create a new Info Marker
   - Enter bold/italic text, bullet/numbered lists, and a link in title/body fields
   - Save and confirm the item appears in the list
2. Verify list display
   - Ensure list shows plain text previews (no HTML tags) without breaking
3. Edit existing marker
   - Click Edit and verify `TextEditor` loads previously saved rich formatting
   - Change formatting and save; verify updates persist with formatting intact
4. Secondary entries
   - Add a secondary entry with formatted text; save
   - Edit that secondary entry and verify formatting is intact

## Deployment / Migration

- No schema changes, migrations, or dependency updates required
- No changes to API contracts (still sending/receiving strings)

## Potential Follow-ups

- For rendering formatted content outside of editors, use a dedicated component like `HtmlContentDisplay` to avoid XSS and preserve styling
- Consider server-side sanitization/whitelisting if untrusted input is a concern

## Commit Message

fix(info-markers): preserve rich text formatting end-to-end (create/store/retrieve/edit)

- Keep raw HTML for editing in InfoMarkerList and pass to form
- Use cleaned text only for list display
- Confirm TextEditor returns innerHTML and submission keeps HTML intact
- Ensure API/model store formatted strings without stripping

