# landingPageVideoPlayback state management

This change introduces a global state `landingPageVideoPlayback` controlling whether the hero/landing video should continue playing while the user navigates the 360° viewer.

## What was added
- Global state and action in Experience context:
  - `landingPageVideoPlayback: boolean` (default: `false`)
  - Action: `SET_LANDING_PAGE_VIDEO_PLAYBACK` with boolean payload
- Navigation source detection in `ExperienceContextProvider`:
  - If navigating TO `/360s` FROM `/hero-video` → sets `landingPageVideoPlayback = true`
  - Otherwise (arriving at `/360s` from other sources) → sets to `false`
- `_360LandingPageWrapper` integration:
  - Syncs its local `landingPageVideo` UI flag to the global `landingPageVideoPlayback`
  - On `handleLandingPageBtnClick()` sets `landingPageVideoPlayback = false`, shows navbar, starts audio, and hides the landing page overlay
- Hero video integration:
  - `SimpleHeroVideo` centralizes 360 redirect via `goTo360()`, which first sets `landingPageVideoPlayback = true` then navigates
  - `Skip` control replaced with a client component that dispatches the same flag before redirect

## Files changed
- `src/contexts/reducerExperience.js`
  - Added `landingPageVideoPlayback` to initial state
  - Added `SET_LANDING_PAGE_VIDEO_PLAYBACK` action and reducer case
- `src/contexts/useContextExperience.js`
  - Added route-source tracking using `usePathname()` and effect that updates `landingPageVideoPlayback`
- `src/components/360s/_360LandingPageWrapper.jsx`
  - On click: dispatches `SET_LANDING_PAGE_VIDEO_PLAYBACK: false`
  - Effect to mirror global `landingPageVideoPlayback` to local `landingPageVideo`
- `src/app/(navigation)/hero-video/SimpleHeroVideo.jsx`
  - New `goTo360()` helper dispatches playback flag then `router.push`
  - All redirects now use `goTo360()`
- `src/app/(navigation)/hero-video/page.jsx`
  - Replaced `<Link>` Skip with a client component
- `src/app/(navigation)/hero-video/SkipTo360Button.jsx` (new)
  - Client button that dispatches flag and pushes to `/360s?id=New_entrance_360_002`

## Behavior summary
- Initial state is `false`.
- When user navigates from `/hero-video` to `/360s`, the flag becomes `true` so the landing/hero video can keep playing while inside 360.
- When the user presses the Enter/landing-page button in 360, the flag is set `false` and stays `false` across 360 navigation.
- The flag will only become `true` again when re-entering 360 from the `/hero-video` URL.

## Audio integration
- On enter click, `setPlayAuido(true)` is called as before to start background audio, aligning with the existing audio context and preferences.

## Suggested commit message

feat(360): add landingPageVideoPlayback global state with route-source detection and 360/hero-video integration

- Add landingPageVideoPlayback to Experience state and reducer
- Detect `/hero-video` → `/360s` navigation to enable playback automatically
- Sync `_360LandingPageWrapper` local UI state with global flag
- Ensure Enter button sets playback to false and persists until next hero→360 visit
- Unify hero video redirects via goTo360; add Skip button client component
- Keep audio behavior consistent (play on enter)


